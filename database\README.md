# Balangay Boat Tours Database

## 🚢 Complete Database Schema for Balangay Boat Tours System

This is the **single, organized SQL file** that contains the complete database structure for the Balangay Boat Tours booking system.

## 📁 Files Overview

### ✅ **NEW - Use This File:**
- **`balangay_boat_tours_complete.sql`** - **MAIN DATABASE FILE** (Use this one!)

### ❌ **OLD - Don't Use These:**
- ~~`booking_system.sql`~~ - Old version
- ~~`fixed_booking_system.sql`~~ - Old version  
- ~~`add_passenger_manifest_table.sql`~~ - Already included in main file

## 🛠️ Installation Instructions

### Step 1: Create Database
1. Open phpMyAdmin or MySQL command line
2. Import the file: `balangay_boat_tours_complete.sql`
3. The script will automatically:
   - Create database `balangay_boat_tours`
   - Create all tables
   - Insert sample data
   - Set up stored procedures

### Step 2: Default Login
- **Username:** `admin`
- **Password:** `password`
- **Email:** `<EMAIL>`

### Step 3: Update Config Files
Update your config files to use the new database name:
```php
$dbname = "balangay_boat_tours";
```

## 📊 Database Structure

### Core Tables:
1. **`admin_users`** - Admin login accounts
2. **`boats`** - Boat information and pricing
3. **`boat_availability_dates`** - Specific date availability
4. **`bookings`** - Customer reservations
5. **`passenger_manifest`** - Government compliance (Coast Guard, Marina, Tourism)
6. **`payments`** - Payment tracking
7. **`system_settings`** - System configuration
8. **`activity_logs`** - System activity tracking

### Stored Procedures:
1. **`CheckBoatAvailabilityForDate`** - Check boat availability
2. **`GetPassengerManifest`** - Get passenger list for booking
3. **`GetBookingWithManifest`** - Get complete booking details
4. **`ExportPassengerManifestForDate`** - Government reporting

## 🎯 Key Features

### ✅ **Government Compliance**
- Passenger manifest for Coast Guard requirements
- Tourism office reporting capabilities
- Marina safety compliance

### ✅ **Payment Processing**
- GCash integration support
- Manual payment tracking
- Payment status management

### ✅ **Admin Dashboard**
- Booking analytics
- Revenue tracking
- Customer management

### ✅ **Booking System**
- Real-time availability checking
- Automatic booking code generation
- Email notifications

## 🔧 System Settings

The database includes configurable settings:
- Company information
- Payment details (GCash)
- Booking rules
- Government reporting options

## 📝 Sample Data Included

- **4 Sample Boats:**
  - Balangay Heritage (Traditional)
  - Island Explorer (Modern)
  - Sunset Cruiser (Luxury)
  - Adventure Seeker (Speed Boat)

- **Default Admin Account**
- **System Configuration**

## 🚀 Next Steps

1. Import the SQL file
2. Update config files
3. Test admin login
4. Configure system settings
5. Add real boat data
6. Test booking process

## 💡 Notes

- Database name changed to `balangay_boat_tours`
- All old SQL files can be deleted
- Passenger manifest is required for government compliance
- System supports both GCash and manual payments

---

**Developed by:** 4th Year BSIT Students  
**College:** College of Information and Computing Studies  
**Academic Year:** 2024-2025
