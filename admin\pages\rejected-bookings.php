<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection
include('../includes/config.php');
// Database connection is already included in config.php

// Test database connection
if (!$con) {
    die("Database connection failed: " . mysqli_connect_error());
}

// Test query to check if database is accessible
$test_query = mysqli_query($con, "SELECT 1");
if (!$test_query) {
    die("Database query failed: " . mysqli_error($con));
}

// Validating Session
if (!isset($_SESSION['aid']) || strlen($_SESSION['aid']) == 0) {
    header('location:../../../../php/pages/admin-login.php');
    exit();
}

  ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | Rejected Bookings</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("../includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("../includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Rejected Bookings</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">Rejected Bookings</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-danger">
                <h3 class="card-title text-white">
                  <i class="fas fa-times-circle"></i> Rejected Bookings
                </h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus text-white"></i>
                  </button>
                  <button type="button" class="btn btn-tool" onclick="location.reload()">
                    <i class="fas fa-sync-alt text-white"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table id="example1" class="table table-bordered table-striped">
                    <thead class="thead-dark">
                      <tr>
                        <th>#</th>
                        <th>Reference No</th>
                        <th>Customer Name</th>
                        <th>Email</th>
                        <th>Contact No</th>
                        <th>Boat</th>
                        <th>Destination</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Total Amount</th>
                        <th>Status</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
<?php
// First update any bookings with future dates (2025)
$update_sql = "UPDATE bookings
SET
    start_date = DATE_SUB(NOW(), INTERVAL 10 DAY),
    end_date = DATE_SUB(NOW(), INTERVAL 9 DAY),
    booking_time = DATE_SUB(NOW(), INTERVAL 12 DAY),
    created_at = DATE_SUB(NOW(), INTERVAL 13 DAY)
WHERE
    booking_status = 'cancelled' AND YEAR(created_at) = 2025";
$con->query($update_sql);

// Simple query to get all cancelled bookings using prepared statement
$sql = "SELECT
    b.booking_id,
    b.booking_code,
    b.start_date,
    b.booking_time,
    b.total,
    b.booking_status,
    c.first_name,
    c.last_name,
    c.email,
    c.contact_number,
    bt.name as boat_name,
    d.name as destination_name
    FROM bookings b
    JOIN customers c ON b.customer_id = c.customer_id
    JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN destinations d ON b.destination_id = d.destination_id
    WHERE b.booking_status = 'cancelled'
    ORDER BY b.created_at DESC";
$stmt = $con->prepare($sql);
$stmt->execute();
$query = $stmt->get_result();

if (!$query) {
    echo '<tr><td colspan="12" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading bookings: ' . mysqli_error($con) . '</td></tr>';
} else {
    $cnt = 1;
    $hasRows = false;
    while($row = mysqli_fetch_assoc($query)) {
        $hasRows = true;
        $status = $row['booking_status'];
        $status_class = 'bg-danger';
        $status_text = 'Cancelled';

        // Format the booking time
        $booking_time = date('H:i', strtotime($row['booking_time']));

        echo '<tr>';
        echo '<td>' . $cnt . '</td>';
        echo '<td>' . htmlspecialchars($row['booking_code']) . '</td>';
        echo '<td>' . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . '</td>';
        echo '<td>' . htmlspecialchars($row['email']) . '</td>';
        echo '<td>' . htmlspecialchars($row['contact_number']) . '</td>';
        echo '<td>' . htmlspecialchars($row['boat_name']) . '</td>';
        echo '<td>' . htmlspecialchars($row['destination_name']) . '</td>';
        echo '<td>' . date('M d, Y', strtotime($row['start_date'])) . '</td>';
        echo '<td>' . $booking_time . '</td>';
        echo '<td>&#8369; ' . number_format($row['total'], 2) . '</td>';
        echo '<td><span class="badge ' . $status_class . '">' . $status_text . '</span></td>';
        echo '<td>';
        echo '<div class="btn-group" role="group">';
        echo '<button class="btn btn-info btn-sm view-btn" data-id="' . $row['booking_id'] . '" title="View Details">
            <i class="fas fa-eye"></i>
        </button>';
        echo '<button class="btn btn-warning btn-sm edit-btn" data-id="' . $row['booking_id'] . '" title="Edit Booking">
            <i class="fas fa-edit"></i>
        </button>';
        echo '<button class="btn btn-danger btn-sm delete-btn" data-id="' . $row['booking_id'] . '" title="Delete Booking">
            <i class="fas fa-trash"></i>
        </button>';
        echo '</div>';
        echo '</td>';
        echo '</tr>';
        $cnt++;
    }
    if (!$hasRows) {
        echo '<tr><td colspan="12" class="text-center"><i class="fas fa-info-circle"></i> No rejected or cancelled bookings found.</td></tr>';
    }
}
?>
                  </tbody>
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <!-- Delete Confirmation Modal -->
  <div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header bg-danger">
          <h5 class="modal-title text-white" id="deleteConfirmationModalLabel">
            <i class="fas fa-exclamation-triangle"></i> Confirm Delete
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="text-center">
            <i class="fas fa-trash-alt fa-3x mb-3 text-danger"></i>
            <p class="mb-0">Are you sure you want to delete this booking?</p>
            <p class="text-muted small">This action cannot be undone.</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            <i class="fas fa-times"></i> Cancel
          </button>
          <button type="button" class="btn btn-danger" id="confirmDelete">
            <i class="fas fa-trash"></i> Delete
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Booking Details Modal -->
  <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
            <i class="fas fa-info-circle"></i> Booking Details
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered table-striped" id="bookingDetailsTable">
              <tr><th>First Name:</th><td id="viewFirstName"></td></tr>
              <tr><th>Last Name:</th><td id="viewLastName"></td></tr>
              <tr><th>Age:</th><td id="viewAge"></td></tr>
              <tr><th>Sex:</th><td id="viewSex"></td></tr>
              <tr><th>Contact Number:</th><td id="viewContactNumber"></td></tr>
              <tr><th>Email:</th><td id="viewEmail"></td></tr>
              <tr><th>Address:</th><td id="viewAddress"></td></tr>
              <tr><th>Tour Destination:</th><td id="viewDestination"></td></tr>
              <tr><th>Number of Pax:</th><td id="viewNoOfPax"></td></tr>
              <tr><th>Start Date:</th><td id="viewStartDate"></td></tr>
              <tr><th>End Date:</th><td id="viewEndDate"></td></tr>
              <tr><th>Duration:</th><td id="viewDuration"></td></tr>
              <tr><th>Booking Time:</th><td id="viewBookingTime"></td></tr>
              <tr><th>Selected Boat:</th><td id="viewBoat"></td></tr>
              <tr><th>Boat Price:</th><td id="viewBoatPrice"></td></tr>
              <tr><th>Booking Code:</th><td id="viewBookingCode"></td></tr>
              <tr><th>Environmental Fee:</th><td id="viewEnvironmentalFee"></td></tr>
              <tr><th>Payment Method:</th><td id="viewPaymentMethod"></td></tr>
              <tr><th>Total Amount:</th><td id="viewTotal"></td></tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Booking Modal -->
  <div class="modal fade" id="editBookingModal" tabindex="-1" role="dialog" aria-labelledby="editBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-primary">
          <h5 class="modal-title text-white" id="editBookingModalLabel">
            <i class="fas fa-edit"></i> Edit Booking
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="editBookingForm">
            <input type="hidden" name="booking_id" id="editBookingId">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>Booking Code</label>
                  <input type="text" class="form-control" id="editBookingCode" readonly>
                </div>
                <div class="form-group">
                  <label>Customer Name</label>
                  <input type="text" class="form-control" id="editCustomerName" readonly>
                </div>
                <div class="form-group">
                  <label>Email</label>
                  <input type="email" class="form-control" id="editEmail" readonly>
                </div>
                <div class="form-group">
                  <label>Contact Number</label>
                  <input type="text" class="form-control" id="editContactNumber" readonly>
                </div>
                <div class="form-group">
                  <label>Address</label>
                  <input type="text" class="form-control" id="editAddress" name="address">
                </div>
                <div class="form-group">
                  <label>Age</label>
                  <input type="number" class="form-control" id="editAge" name="age" min="1" max="120">
                </div>
                <div class="form-group">
                  <label>Sex</label>
                  <select class="form-control" id="editSex" name="sex">
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Boat</label>
                  <select class="form-control" id="editBoat" name="boat_id" required>
                    <option value="">Select Boat</option>
                  </select>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>Destination</label>
                  <select class="form-control" id="editDestination" name="destination_id" required>
                    <option value="">Select Destination</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Start Date</label>
                  <input type="date" class="form-control" id="editStartDate" name="start_date" required>
                </div>
                <div class="form-group">
                  <label>End Date</label>
                  <input type="date" class="form-control" id="editEndDate" name="end_date" required>
                </div>
                <div class="form-group">
                  <label>Booking Time</label>
                  <input type="time" class="form-control" id="editBookingTime" name="booking_time" required>
                </div>
                <div class="form-group">
                  <label>Total Amount</label>
                  <input type="number" class="form-control" id="editTotal" name="total" required>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12">
                <div class="form-group">
                  <label>Number of Pax</label>
                  <input type="number" class="form-control" id="editNoOfPax" name="no_of_pax" required>
                </div>
                <div class="form-group">
                  <label>Environmental Fee</label>
                  <input type="number" step="0.01" class="form-control" id="editEnvironmentalFee" name="environmental_fee">
                </div>
                <div class="form-group">
                  <label>Payment Method</label>
                  <select class="form-control" id="editPaymentMethod" name="payment_method">
                    <option value="gcash">GCash</option>
                    <option value="manual">Manual Payment</option>
                  </select>
                </div>


                <div class="form-group">
                  <label>Booking Status</label>
                  <select class="form-control" id="editStatus" name="booking_status">
                    <option value="pending">Pending</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="saveEdit">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
  $(function () {
    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#example1')) {
      // If already initialized, destroy it first
      $('#example1').DataTable().destroy();
    }

    // Initialize DataTable
    $("#example1").DataTable({
      "responsive": true,
      "lengthChange": false,
      "autoWidth": false,
      "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

    // View booking details - when view button is clicked
    $(document).on('click', '.view-btn', function() {
      // Get booking ID from the button
      var id = $(this).data('id');

      // Get booking details from server
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          var booking = response.data;

          // Format dates for display
          var startDate = new Date(booking.start_date);
          var endDate = new Date(booking.end_date);

          // Calculate duration between dates
          var days = Math.floor((endDate - startDate) / (24 * 60 * 60 * 1000));
          var durationStr = days + ' day(s)';

          // Format currency values
          function formatPeso(amount) {
            return '₱ ' + parseFloat(amount).toFixed(2);
          }

          // Fill in the details in the modal
          $('#viewFirstName').text(booking.first_name || 'Not set');
          $('#viewLastName').text(booking.last_name || 'Not set');
          $('#viewContactNumber').text(booking.contact_number || 'Not set');
          $('#viewEmail').text(booking.email || 'Not set');
          $('#viewDestination').text(booking.destination_name || 'Not set');
          $('#viewNoOfPax').text(booking.no_of_pax || 'Not set');
          $('#viewStartDate').text(startDate.toLocaleDateString());
          $('#viewEndDate').text(endDate.toLocaleDateString());
          $('#viewDuration').text(durationStr);
          $('#viewBookingTime').text(booking.booking_time || 'Not set');
          $('#viewBoat').text(booking.boat_name || 'Not set');
          $('#viewBookingCode').text(booking.booking_code || 'Not set');
          $('#viewTotal').text(formatPeso(booking.total));

          // Show the modal with booking details
          $('#bookingDetailsModal').modal('show');
        } else {
          alert('Could not load booking details');
        }
      }, 'json').fail(function() {
        alert('Error connecting to server');
      });
    });

    // Edit booking - when edit button is clicked
    $(document).on('click', '.edit-btn', function() {
      // Get the booking ID from the button's data attribute
      var id = $(this).data('id');

      // Validate booking ID
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Please try again or contact support.');
        console.error('Invalid booking ID detected:', id);
        return;
      }

      // Reset form fields
      $('#editBookingForm')[0].reset();

      // First load the dropdown options for boats and destinations
      loadBoatsAndDestinations();

      // Show loading indicator in the modal
      var originalModalContent = $('#editBookingModal .modal-body').html();
      $('#editBookingModal .modal-body').html('<div class="text-center p-5"><i class="fas fa-spinner fa-spin fa-3x"></i><p class="mt-3">Loading booking details...</p></div>');
      $('#editBookingModal').modal('show');

      // Get booking details from server
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          var booking = response.data;

          console.log("Booking data received:", booking); // Debug log

          // Restore the form content
          $('#editBookingModal .modal-body').html(originalModalContent);

          // Fill in the form with booking data
          $('#editBookingId').val(booking.booking_id);
          $('#editBookingCode').val(booking.booking_code || 'N/A');
          $('#editCustomerName').val((booking.first_name || '') + ' ' + (booking.last_name || ''));
          $('#editEmail').val(booking.email || '');
          $('#editContactNumber').val(booking.contact_number || '');
          $('#editAddress').val(booking.address || '');
          $('#editAge').val(booking.age || '');
          $('#editSex').val(booking.sex || 'Male');
          $('#editNoOfPax').val(booking.no_of_pax || 1);
          $('#editEnvironmentalFee').val(booking.environmental_fee || 0);
          $('#editDropoffLocation').val(booking.dropoff_location || '');

          // Set dropdown values after a short delay to ensure they're loaded
          setTimeout(function() {
            $('#editBoat').val(booking.boat_id);
            $('#editDestination').val(booking.destination_id);
            $('#editPaymentMethod').val(booking.payment_method || 'cash');
            $('#editStatus').val(booking.booking_status || 'cancelled');
          }, 500);

          // Set date and time fields
          if (booking.start_date && booking.start_date !== 'N/A') {
            $('#editStartDate').val(booking.start_date);
          }

          if (booking.end_date && booking.end_date !== 'N/A') {
            $('#editEndDate').val(booking.end_date);
          }

          if (booking.booking_time && booking.booking_time !== 'N/A') {
            $('#editBookingTime').val(booking.booking_time);
          }

          $('#editTotal').val(booking.total || 0);
        } else {
          // Show more detailed error message
          var errorMsg = response.error || 'Unknown error occurred';
          $('#editBookingModal .modal-body').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Could not load booking details: ' + errorMsg + '</div>');
          console.error('Error loading booking details:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        $('#editBookingModal .modal-body').html('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> Error connecting to server. Please try again later.</div>');
        console.error('AJAX error during edit:', textStatus, errorThrown);
      });
    });

    // Delete booking - when delete button is clicked
    $('.delete-btn').click(function() {
      // Get booking ID from the button
      var id = $(this).data('id');

      // Validate booking ID
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Cannot delete this booking.');
        console.error('Invalid booking ID detected for delete:', id);
        return;
      }

      // First verify the booking exists before showing delete confirmation
      $.post('get-booking-details.php', { id: id }, function(response) {
        if(response.success && response.data) {
          // Booking exists, show confirmation modal
          $('#deleteConfirmationModal').modal('show');
          $('#confirmDelete').data('booking-id', id);
        } else {
          alert('Error: ' + (response.error || 'Booking not found'));
          console.error('Booking verification error:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        alert('Error connecting to server. Please try again later.');
        console.error('AJAX error during delete verification:', textStatus, errorThrown);
      });
    });

    // When delete is confirmed
    $('#confirmDelete').click(function() {
      var id = $(this).data('booking-id');

      // Validate booking ID again as a safeguard
      if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
        alert('Invalid booking ID. Cannot delete this booking.');
        $('#deleteConfirmationModal').modal('hide');
        return;
      }

      // Send delete request to server
      $.post('delete-booking.php', { id: id }, function(response) {
        if(response.success) {
          $('#deleteConfirmationModal').modal('hide');
          alert('Booking deleted successfully');
          location.reload(); // Refresh the page
        } else {
          alert(response.error || 'Error deleting booking');
          console.error('Delete booking error:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        alert('Error connecting to server. Please try again later.');
        console.error('AJAX error during delete:', textStatus, errorThrown);
        $('#deleteConfirmationModal').modal('hide');
      });
    });

    // Save edited booking
    $('#saveEdit').click(function() {
      // Get all form data as a serialized string
      var formData = $('#editBookingForm').serialize();

      // Add customer name to form data
      var customerName = $('#editCustomerName').val();
      formData += '&customer_name=' + encodeURIComponent(customerName);

      // Add destination name to form data
      var destinationId = $('#editDestination').val();
      var destinationName = $('#editDestination option:selected').text();
      formData += '&destination_name=' + encodeURIComponent(destinationName);

      // Add boat name to form data
      var boatId = $('#editBoat').val();
      var boatName = $('#editBoat option:selected').text();
      formData += '&boat_name=' + encodeURIComponent(boatName);

      console.log('Form data with added fields:', formData);

      // Basic validation for required fields
      var requiredFields = ['boat_id', 'destination_id', 'start_date', 'end_date',
                           'booking_time', 'no_of_pax', 'total'];

      var missingFields = [];
      requiredFields.forEach(function(field) {
        var value = $('[name="' + field + '"]').val();
        if (!value) {
          missingFields.push(field.replace('_', ' '));
        }
      });

      if (missingFields.length > 0) {
        alert('Please fill in the following required fields: ' + missingFields.join(', '));
        return;
      }

      // Validate dates
      var startDate = new Date($('#editStartDate').val());
      var endDate = new Date($('#editEndDate').val());

      if (endDate < startDate) {
        alert('End date cannot be before start date');
        return;
      }

      // Show loading state
      $('#saveEdit').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');

      // Send data to server
      $.ajax({
        url: 'update-booking.php',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
          if(response.success) {
            $('#editBookingModal').modal('hide');
            alert('Booking updated successfully');
            location.reload();
          } else {
            $('#saveEdit').prop('disabled', false).html('Save Changes');
            alert(response.error || 'Error updating booking');
            console.error('Update error:', response);
          }
        },
        error: function(jqXHR, textStatus, errorThrown) {
          $('#saveEdit').prop('disabled', false).html('Save Changes');
          alert('Error connecting to server. Please try again later.');
          console.error('AJAX error:', textStatus, errorThrown);

          // Try to parse response for more details
          try {
            var errorResponse = JSON.parse(jqXHR.responseText);
            console.error('Server error details:', errorResponse);
          } catch(e) {
            console.error('Raw server response:', jqXHR.responseText);
          }
        }
      });
    });

    // Function to load boats and destinations for the dropdown menus
    function loadBoatsAndDestinations() {
      // Load boats
      $.get('get-options.php', { type: 'boats' }, function(data) {
        // Add default option
        var boatOptions = '<option value="">Select Boat</option>' + data;
        $('#editBoat').html(boatOptions);
      }).fail(function(jqXHR, textStatus, errorThrown) {
        console.error('Error loading boats:', textStatus, errorThrown);
        $('#editBoat').html('<option value="">Error loading boats</option>');
      });

      // Load destinations
      $.get('get-options.php', { type: 'destinations' }, function(data) {
        // Add default option
        var destOptions = '<option value="">Select Destination</option>' + data;
        $('#editDestination').html(destOptions);
      }).fail(function(jqXHR, textStatus, errorThrown) {
        console.error('Error loading destinations:', textStatus, errorThrown);
        $('#editDestination').html('<option value="">Error loading destinations</option>');
      });
    }
  });
</script>
</body>
</html>