// Test script for passenger count synchronization
// Run this in the browser console on the booking page

console.log('🧪 Testing Passenger Count Synchronization...');

function testPassengerSync() {
    console.log('\n=== PASSENGER SYNC TEST ===');
    
    // Test 1: Check if elements exist
    console.log('\n1. Checking if required elements exist...');
    const totalPassengers = document.getElementById('totalPassengers');
    const regularPax = document.getElementById('regularPax');
    const discountedPax = document.getElementById('discountedPax');
    const childrenPax = document.getElementById('childrenPax');
    const infantsPax = document.getElementById('infantsPax');
    
    console.log('✓ totalPassengers:', !!totalPassengers);
    console.log('✓ regularPax:', !!regularPax);
    console.log('✓ discountedPax:', !!discountedPax);
    console.log('✓ childrenPax:', !!childrenPax);
    console.log('✓ infantsPax:', !!infantsPax);
    
    if (!totalPassengers || !regularPax || !discountedPax || !childrenPax || !infantsPax) {
        console.error('❌ Some required elements are missing!');
        return;
    }
    
    // Test 2: Reset all values
    console.log('\n2. Resetting all values...');
    totalPassengers.value = 1;
    regularPax.value = 0;
    discountedPax.value = 0;
    childrenPax.value = 0;
    infantsPax.value = 0;
    
    // Test 3: Test scenario from user's example
    console.log('\n3. Testing user scenario: 4 Senior Citizens/PWD + 2 Adults + 1 Child = 7 total');
    
    // Set Senior Citizens/PWD to 4
    discountedPax.value = 4;
    if (typeof window.increaseFeeCount === 'function') {
        // Trigger the sync by calling the function
        window.updateTotalPassengersFromFees();
    }
    console.log('After setting 4 Senior Citizens/PWD, totalPassengers =', totalPassengers.value);
    
    // Add 2 Adults
    regularPax.value = 2;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }
    console.log('After adding 2 Adults, totalPassengers =', totalPassengers.value);
    
    // Add 1 Child
    childrenPax.value = 1;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }
    console.log('After adding 1 Child, totalPassengers =', totalPassengers.value);
    
    // Test 4: Check totals
    console.log('\n4. Checking final totals...');
    const finalRegular = parseInt(regularPax.value) || 0;
    const finalDiscounted = parseInt(discountedPax.value) || 0;
    const finalChildren = parseInt(childrenPax.value) || 0;
    const finalInfants = parseInt(infantsPax.value) || 0;
    const finalTotal = parseInt(totalPassengers.value) || 0;
    
    const calculatedTotal = finalRegular + finalDiscounted + finalChildren + finalInfants;
    
    console.log('Regular Adults:', finalRegular);
    console.log('Senior Citizens/PWD:', finalDiscounted);
    console.log('Children:', finalChildren);
    console.log('Infants:', finalInfants);
    console.log('Calculated Total:', calculatedTotal);
    console.log('Total Passengers Field:', finalTotal);
    
    if (calculatedTotal === finalTotal) {
        console.log('✅ SUCCESS: Totals match!');
    } else {
        console.log('❌ FAIL: Totals do not match!');
    }
    
    // Test 5: Test reverse sync (totalPassengers -> environmental fees)
    console.log('\n5. Testing reverse sync (totalPassengers -> environmental fees)...');
    totalPassengers.value = 10;
    if (typeof window.syncEnvironmentalFeesWithPassengers === 'function') {
        window.syncEnvironmentalFeesWithPassengers(10);
    }
    
    const newTotal = parseInt(regularPax.value) + parseInt(discountedPax.value) + parseInt(childrenPax.value) + parseInt(infantsPax.value);
    console.log('After setting totalPassengers to 10, environmental fees total =', newTotal);
    
    if (newTotal === 10) {
        console.log('✅ SUCCESS: Reverse sync works!');
    } else {
        console.log('❌ FAIL: Reverse sync failed!');
    }
    
    console.log('\n=== TEST COMPLETE ===');
}

// Run the test
testPassengerSync();

// Also provide manual test instructions
console.log('\n📋 MANUAL TEST INSTRUCTIONS:');
console.log('1. Try using the +/- buttons on Senior Citizens/PWD to add 4');
console.log('2. Try using the +/- buttons on Adults to add 2'); 
console.log('3. Try using the +/- buttons on Children to add 1');
console.log('4. Check if Total Passengers shows 7');
console.log('5. Try changing Total Passengers directly and see if environmental fees update');
