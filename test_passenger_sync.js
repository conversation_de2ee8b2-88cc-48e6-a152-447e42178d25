// Test script for passenger count synchronization
// Run this in the browser console on the booking page

console.log('🧪 Testing Passenger Count Synchronization...');

function testPassengerSync() {
    console.log('\n=== PASSENGER SYNC TEST ===');
    
    // Test 1: Check if elements exist
    console.log('\n1. Checking if required elements exist...');
    const totalPassengers = document.getElementById('totalPassengers');
    const regularPax = document.getElementById('regularPax');
    const discountedPax = document.getElementById('discountedPax');
    const childrenPax = document.getElementById('childrenPax');
    const infantsPax = document.getElementById('infantsPax');
    
    console.log('✓ totalPassengers:', !!totalPassengers);
    console.log('✓ regularPax:', !!regularPax);
    console.log('✓ discountedPax:', !!discountedPax);
    console.log('✓ childrenPax:', !!childrenPax);
    console.log('✓ infantsPax:', !!infantsPax);
    
    if (!totalPassengers || !regularPax || !discountedPax || !childrenPax || !infantsPax) {
        console.error('❌ Some required elements are missing!');
        return;
    }
    
    // Test 2: Reset all values
    console.log('\n2. Resetting all values...');
    totalPassengers.value = 1;
    regularPax.value = 0;
    discountedPax.value = 0;
    childrenPax.value = 0;
    infantsPax.value = 0;
    
    // Test 3: Test scenario from user's example
    console.log('\n3. Testing user scenario: 4 Senior Citizens/PWD + 2 Adults + 1 Child = 7 total');
    
    // Set Senior Citizens/PWD to 4
    discountedPax.value = 4;
    if (typeof window.increaseFeeCount === 'function') {
        // Trigger the sync by calling the function
        window.updateTotalPassengersFromFees();
    }
    console.log('After setting 4 Senior Citizens/PWD, totalPassengers =', totalPassengers.value);
    
    // Add 2 Adults
    regularPax.value = 2;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }
    console.log('After adding 2 Adults, totalPassengers =', totalPassengers.value);
    
    // Add 1 Child
    childrenPax.value = 1;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }
    console.log('After adding 1 Child, totalPassengers =', totalPassengers.value);
    
    // Test 4: Check totals
    console.log('\n4. Checking final totals...');
    const finalRegular = parseInt(regularPax.value) || 0;
    const finalDiscounted = parseInt(discountedPax.value) || 0;
    const finalChildren = parseInt(childrenPax.value) || 0;
    const finalInfants = parseInt(infantsPax.value) || 0;
    const finalTotal = parseInt(totalPassengers.value) || 0;
    
    const calculatedTotal = finalRegular + finalDiscounted + finalChildren + finalInfants;
    
    console.log('Regular Adults:', finalRegular);
    console.log('Senior Citizens/PWD:', finalDiscounted);
    console.log('Children:', finalChildren);
    console.log('Infants:', finalInfants);
    console.log('Calculated Total:', calculatedTotal);
    console.log('Total Passengers Field:', finalTotal);
    
    if (calculatedTotal === finalTotal) {
        console.log('✅ SUCCESS: Totals match!');
    } else {
        console.log('❌ FAIL: Totals do not match!');
    }
    
    // Test 5: Test reverse sync (totalPassengers -> environmental fees)
    console.log('\n5. Testing reverse sync (totalPassengers -> environmental fees)...');
    totalPassengers.value = 10;
    if (typeof window.syncEnvironmentalFeesWithPassengers === 'function') {
        window.syncEnvironmentalFeesWithPassengers(10);
    }
    
    const newTotal = parseInt(regularPax.value) + parseInt(discountedPax.value) + parseInt(childrenPax.value) + parseInt(infantsPax.value);
    console.log('After setting totalPassengers to 10, environmental fees total =', newTotal);
    
    if (newTotal === 10) {
        console.log('✅ SUCCESS: Reverse sync works!');
    } else {
        console.log('❌ FAIL: Reverse sync failed!');
    }
    
    console.log('\n=== TEST COMPLETE ===');
}

// Test the specific scenario mentioned by user
function testLimitValidation() {
    console.log('\n=== TESTING LIMIT VALIDATION ===');

    // Reset values
    const totalPassengers = document.getElementById('totalPassengers');
    const regularPax = document.getElementById('regularPax');
    const discountedPax = document.getElementById('discountedPax');
    const childrenPax = document.getElementById('childrenPax');

    if (!totalPassengers || !regularPax || !discountedPax || !childrenPax) {
        console.error('❌ Required elements not found!');
        return;
    }

    console.log('\n1. Setting up scenario: 3 total passengers limit');
    totalPassengers.value = 3;
    regularPax.value = 0;
    discountedPax.value = 0;
    childrenPax.value = 0;

    // Update forms to apply the new limit
    if (typeof window.updatePassengerForms === 'function') {
        window.updatePassengerForms();
    }

    console.log('\n2. Adding 2 Adults...');
    regularPax.value = 2;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }

    console.log('\n3. Adding 1 PWD...');
    discountedPax.value = 1;
    if (typeof window.updateTotalPassengersFromFees === 'function') {
        window.updateTotalPassengersFromFees();
    }

    console.log('\n4. Current state:');
    console.log('- Adults:', regularPax.value);
    console.log('- PWD:', discountedPax.value);
    console.log('- Total:', parseInt(regularPax.value) + parseInt(discountedPax.value));
    console.log('- Total Passengers Limit:', totalPassengers.value);

    console.log('\n5. Testing if + buttons are disabled...');

    // Update button states
    if (typeof window.updateFeeButtonStates === 'function') {
        window.updateFeeButtonStates('regularPax');
        window.updateFeeButtonStates('discountedPax');
        window.updateFeeButtonStates('childrenPax');
    }

    // Check button states
    const adultPlusBtn = document.querySelector('#regularPax').closest('.fee-number-input-container').querySelector('.plus-btn');
    const pwdPlusBtn = document.querySelector('#discountedPax').closest('.fee-number-input-container').querySelector('.plus-btn');
    const childPlusBtn = document.querySelector('#childrenPax').closest('.fee-number-input-container').querySelector('.plus-btn');

    console.log('- Adult + button disabled:', adultPlusBtn?.disabled);
    console.log('- PWD + button disabled:', pwdPlusBtn?.disabled);
    console.log('- Child + button disabled:', childPlusBtn?.disabled);

    if (adultPlusBtn?.disabled && pwdPlusBtn?.disabled && childPlusBtn?.disabled) {
        console.log('✅ SUCCESS: All + buttons are properly disabled when limit is reached!');
    } else {
        console.log('❌ FAIL: Some + buttons are not disabled when they should be!');
    }

    console.log('\n6. Now testing if increasing Total Passengers enables buttons...');
    totalPassengers.value = 5;
    if (typeof window.updatePassengerForms === 'function') {
        window.updatePassengerForms();
    }

    console.log('- Adult + button disabled after increasing limit:', adultPlusBtn?.disabled);
    console.log('- PWD + button disabled after increasing limit:', pwdPlusBtn?.disabled);
    console.log('- Child + button disabled after increasing limit:', childPlusBtn?.disabled);

    if (!adultPlusBtn?.disabled && !pwdPlusBtn?.disabled && !childPlusBtn?.disabled) {
        console.log('✅ SUCCESS: + buttons are enabled when limit is increased!');
    } else {
        console.log('❌ FAIL: + buttons should be enabled when limit is increased!');
    }

    console.log('\n=== LIMIT VALIDATION TEST COMPLETE ===');
}

// Run the tests
testPassengerSync();
testLimitValidation();
testEnvironmentalFeesPreservation();

// Test the specific issue mentioned by user
function testEnvironmentalFeesPreservation() {
    console.log('\n=== TESTING ENVIRONMENTAL FEES PRESERVATION ===');

    const totalPassengers = document.getElementById('totalPassengers');
    const regularPax = document.getElementById('regularPax');
    const discountedPax = document.getElementById('discountedPax');
    const childrenPax = document.getElementById('childrenPax');
    const infantsPax = document.getElementById('infantsPax');

    if (!totalPassengers || !regularPax || !discountedPax || !childrenPax || !infantsPax) {
        console.error('❌ Required elements not found!');
        return;
    }

    console.log('\n1. Setting up user scenario: 1 Adult, 1 PWD, 1 Child, 1 Infant = 4 total');
    totalPassengers.value = 4;
    regularPax.value = 1;
    discountedPax.value = 1;
    childrenPax.value = 1;
    infantsPax.value = 1;

    console.log('Before change:');
    console.log('- Adults:', regularPax.value);
    console.log('- PWD:', discountedPax.value);
    console.log('- Children:', childrenPax.value);
    console.log('- Infants:', infantsPax.value);
    console.log('- Total Passengers:', totalPassengers.value);

    console.log('\n2. Clicking minus on Number of Passengers (4 → 3)...');
    totalPassengers.value = 3;

    // Trigger the sync function
    if (typeof window.syncEnvironmentalFeesWithPassengers === 'function') {
        window.syncEnvironmentalFeesWithPassengers(3);
    }

    console.log('\nAfter change:');
    console.log('- Adults:', regularPax.value);
    console.log('- PWD:', discountedPax.value);
    console.log('- Children:', childrenPax.value);
    console.log('- Infants:', infantsPax.value);
    console.log('- Total Passengers:', totalPassengers.value);

    // Check if the breakdown was preserved
    const adultValue = parseInt(regularPax.value);
    const pwdValue = parseInt(discountedPax.value);
    const childValue = parseInt(childrenPax.value);
    const infantValue = parseInt(infantsPax.value);

    if (adultValue === 1 && pwdValue === 1 && childValue === 1 && infantValue === 1) {
        console.log('✅ SUCCESS: Environmental fees breakdown preserved!');
    } else if (adultValue === 3 && pwdValue === 0 && childValue === 0 && infantValue === 0) {
        console.log('❌ FAIL: Environmental fees were reset to all adults (this is the bug!)');
    } else {
        console.log('❓ UNEXPECTED: Environmental fees changed in an unexpected way');
    }

    console.log('\n=== ENVIRONMENTAL FEES PRESERVATION TEST COMPLETE ===');
}

// Also provide manual test instructions
console.log('\n📋 MANUAL TEST INSTRUCTIONS:');
console.log('1. Set "Number of Passengers" to 4');
console.log('2. Set Adults to 1, PWD to 1, Children to 1, Infants to 1');
console.log('3. Click minus on "Number of Passengers" (4 → 3)');
console.log('4. Environmental fees should STAY: 1 Adult, 1 PWD, 1 Child, 1 Infant');
console.log('5. Adults should NOT change to 3 or 4!');
console.log('6. The breakdown should be preserved exactly as you set it');
