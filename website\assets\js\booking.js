// IMMEDIATE FUNCTION DEFINITIONS (to ensure they load first)
console.log('🔥 IMMEDIATE: Defining plus/minus functions...');

// Define functions immediately and globally
window.increaseFeeCount = function(fieldId) {
    console.log('🔥 IMMEDIATE increaseFeeCount called for:', fieldId);
    const input = document.getElementById(fieldId);
    if (!input) {
        console.error('Input not found:', fieldId);
        return;
    }
    const currentValue = parseInt(input.value) || 0;
    const maxValue = parseInt(input.getAttribute('max')) || 25;
    if (currentValue < maxValue) {
        input.value = currentValue + 1;
        input.dispatchEvent(new Event('change'));
        if (typeof window.calculateTotalFees === 'function') {
            window.calculateTotalFees();
        }
        window.updateFeeButtonStates(fieldId);
        // Update totalPassengers to match environmental fees total
        window.updateTotalPassengersFromFees();
    }
};

window.decreaseFeeCount = function(fieldId) {
    console.log('🔥 IMMEDIATE decreaseFeeCount called for:', fieldId);
    const input = document.getElementById(fieldId);
    if (!input) {
        console.error('Input not found:', fieldId);
        return;
    }
    const currentValue = parseInt(input.value) || 0;
    const minValue = parseInt(input.getAttribute('min')) || 0;
    if (currentValue > minValue) {
        input.value = currentValue - 1;
        input.dispatchEvent(new Event('change'));
        if (typeof window.calculateTotalFees === 'function') {
            window.calculateTotalFees();
        }
        window.updateFeeButtonStates(fieldId);
        // Update totalPassengers to match environmental fees total
        window.updateTotalPassengersFromFees();
    }
};

window.updateFeeButtonStates = function(fieldId) {
    console.log('🔥 IMMEDIATE updateFeeButtonStates called for:', fieldId);

    // Removed numberOfPax field check - field no longer exists

    const input = document.getElementById(fieldId);
    if (!input) {
        console.error('Input not found for fieldId:', fieldId);
        return;
    }

    const container = input.closest('.fee-number-input-container');
    if (!container) {
        console.error('Container not found for fieldId:', fieldId);
        return;
    }

    const minusBtn = container.querySelector('.minus-btn');
    const plusBtn = container.querySelector('.plus-btn');
    if (!minusBtn || !plusBtn) {
        console.error('Buttons not found for fieldId:', fieldId);
        return;
    }

    const currentValue = parseInt(input.value) || 0;
    const minValue = parseInt(input.getAttribute('min')) || 0;
    const maxValue = parseInt(input.getAttribute('max')) || 25;

    // Update minus button state
    minusBtn.disabled = (currentValue <= minValue);

    // Update plus button state
    plusBtn.disabled = (currentValue >= maxValue);
};

console.log('🔥 IMMEDIATE: Functions defined and assigned to window');
console.log('🔥 IMMEDIATE: window.increaseFeeCount =', typeof window.increaseFeeCount);
console.log('🔥 IMMEDIATE: window.decreaseFeeCount =', typeof window.decreaseFeeCount);

// Global variables
let currentStep = 1;
const totalSteps = 3;

// Debug: Log when script loads
console.log('booking.js script loaded');

// Note: Main function definitions moved to top of file to prevent duplicates

// Quick test function for plus/minus buttons
function testButtonsQuick() {
    console.log('=== QUICK BUTTON TEST ===');

    // Test if functions exist
    console.log('increaseFeeCount exists:', typeof increaseFeeCount === 'function');
    console.log('decreaseFeeCount exists:', typeof decreaseFeeCount === 'function');
    console.log('updateFeeButtonStates exists:', typeof updateFeeButtonStates === 'function');

    // Test if elements exist
    const regularPax = document.getElementById('regularPax');
    console.log('regularPax input exists:', !!regularPax);

    if (regularPax) {
        console.log('Current regularPax value:', regularPax.value);

        // Test increase
        increaseFeeCount('regularPax');
        console.log('After increase:', regularPax.value);

        // Test decrease
        decreaseFeeCount('regularPax');
        console.log('After decrease:', regularPax.value);
    }

    console.log('=== TEST COMPLETE ===');
}

// Note: Functions already assigned to window object at top of file

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing booking.js');

    // Reset currentStep to 1 on page load
    currentStep = 1;

    // Ensure localStorage does not override the currentStep
    const savedData = localStorage.getItem('bookingFormData');
    if (savedData) {
        const formData = JSON.parse(savedData);
        if (formData && formData.currentStep) {
            formData.currentStep = 1;
            localStorage.setItem('bookingFormData', JSON.stringify(formData));
        }
    }

    // Initialize date pickers
    initializeDatePickers();

    // Set up event listeners
    setupEventListeners();

    // Initialize calculations
    calculateTotalFees();
    updateDateTimeSummary();
    updateBookingTime();

    // Generate booking ID
    generateBookingId();

    // Initialize tooltips
    initializeTooltips();

    // Initialize hidden fields
    initializeHiddenFields();

    // Set current date for Today's Bookings
    setCurrentDate();

    // Load saved form data if available
    loadFormData();

    // Save form data on any input change
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', saveFormData);
        element.addEventListener('input', saveFormData);
    });

    // Save form data before page unload
    window.addEventListener('beforeunload', function() {
        console.log('Saving form data before page unload');
        saveFormData();
    });

    // Force a save of the form data after a short delay
    // This ensures any URL parameters are properly saved to localStorage
    setTimeout(function() {
        console.log('Forcing initial save of form data');
        saveFormData();
    }, 1000);

    // Initialize mobile menu functionality
    initializeMobileMenu();



    // Initialize fee button states
    initializeFeeButtonStates();

    // Initialize drop-off location validation
    initializeDropOffValidation();

    // Initialize passenger forms
    updatePassengerForms();

});

// Global date picker variables
window.startDatePicker = null;
window.endDatePicker = null;

// Initialize date pickers
function initializeDatePickers() {
    console.log('Initializing date pickers');

    try {
        // Start date picker
        window.startDatePicker = flatpickr("#startDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_, dateStr) {
                // Use underscore for unused parameter
                if (window.endDatePicker) {
                    window.endDatePicker.set("minDate", dateStr);
                }
                updateDateTimeSummary();
            }
        });

        // End date picker
        window.endDatePicker = flatpickr("#endDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_) {
                // Use underscore for unused parameter
                updateDateTimeSummary();
            }
        });

        console.log('Date pickers initialized successfully:', {
            startDatePicker: window.startDatePicker ? 'OK' : 'Failed',
            endDatePicker: window.endDatePicker ? 'OK' : 'Failed'
        });
    } catch (error) {
        console.error('Error initializing date pickers:', error);

        // Create a fallback for date pickers if flatpickr fails
        if (!window.startDatePicker) {
            console.log('Creating fallback for start date picker');
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.type = 'date';
                startDateInput.min = new Date().toISOString().split('T')[0];
                startDateInput.addEventListener('change', function() {
                    const endDateInput = document.getElementById('endDate');
                    if (endDateInput) {
                        endDateInput.min = this.value;
                    }
                    updateDateTimeSummary();
                });
            }
        }

        if (!window.endDatePicker) {
            console.log('Creating fallback for end date picker');
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.type = 'date';
                endDateInput.min = new Date().toISOString().split('T')[0];
                endDateInput.addEventListener('change', updateDateTimeSummary);
            }
        }
    }
}

// Set up event listeners
function setupEventListeners() {
    // Form submission
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', onBookingFormSubmit);

        // Add direct click event listener to the submit button
        const submitButton = document.querySelector('.btn-submit');
        if (submitButton) {
            console.log('Adding click event listener to submit button');
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Submit button clicked');
                onBookingFormSubmit(e);
            });
        }
    }

    // Gender selection - no longer needed since we removed the "Other" option
    // const genderSelect = document.getElementById('gender');
    // if (genderSelect) {
    //     genderSelect.addEventListener('change', toggleGenderSpecify);
    // }

    // Auto-resize textarea to remove scrollbar
    const addressTextarea = document.getElementById('completeAddress');
    if (addressTextarea) {
        addressTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            // Save form data when textarea changes
            saveFormData();
        });
        // Initial resize
        addressTextarea.style.height = 'auto';
        addressTextarea.style.height = (addressTextarea.scrollHeight) + 'px';
    }

    // Payment options
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', function() {
            const radio = option.querySelector('input[type="radio"]');
            radio.checked = true;
            document.querySelectorAll('.payment-option').forEach(o => o.classList.remove('selected'));
            option.classList.add('selected');

            // Show/hide payment details
            const paymentType = radio.id;
            document.getElementById('gcashDetails').style.display = paymentType === 'gcash' ? 'block' : 'none';
            document.getElementById('manualDetails').style.display = paymentType === 'manual' ? 'block' : 'none';

            // Update payment method in summary
            const summaryPaymentValue = document.getElementById('summaryPaymentValue');
            if (summaryPaymentValue) {
                summaryPaymentValue.textContent = radio.value;
                console.log('Payment method updated in summary:', radio.value);
            }

            // Save form data when payment option changes
            saveFormData();
        });
    });

    // Environmental fee inputs
    ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'].forEach(function(id) {
        const el = document.getElementById(id);
        if (el) {
            el.addEventListener('input', function() {
                calculateTotalFees();
                // Save form data when environmental fees change
                saveFormData();
            });
        }
    });

    // Number of pax
    const numberOfPaxEl = document.getElementById('numberOfPax');
    if (numberOfPaxEl) {
        numberOfPaxEl.addEventListener('input', function() {
            syncNumberOfPax();
            // Save form data when number of pax changes
            saveFormData();
        });
    }

    // Update booking summary when fields change
    const fieldsToUpdate = [
        'firstName', 'lastName', 'age', 'sex', 'contactNumber', 'emailAddress',
        'completeAddress', 'emergencyName', 'emergencyNumber', 'locationTourDestination',
        'dropOffLocation', 'numberOfPax'
    ];

    fieldsToUpdate.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
            field.addEventListener('change', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
        }
    });

    // Save form data when dates change
    document.getElementById('startDate').addEventListener('change', saveFormData);
    document.getElementById('endDate').addEventListener('change', saveFormData);

    // Save form data when navigating between steps
    window.addEventListener('beforeunload', saveFormData);
}

// Initialize tooltips
function initializeTooltips() {
    // Initialize AOS
    AOS.init();
}

// Initialize hidden fields
function initializeHiddenFields() {
    console.log('Initializing hidden fields');

    // Ensure the selectedBoat input exists - always set to "AssignedByTourismOffice"
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    // This means the Tourism Office will be responsible for arranging the boat for tourists
    console.log('Setting boat value to be assigned by tourism office');
    selectedBoatInput.value = 'AssignedByTourismOffice';

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);
    }

    // Debug button removed
}

// Form submission handler
function onBookingFormSubmit(e) {
    e.preventDefault();

    // Confirm booking
    Swal.fire({
        title: 'Complete Booking?',
        text: 'Are you sure you want to complete your booking? You will receive a verification email.', // Alert the user to confirm the booking
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Complete Booking',
        cancelButtonText: 'Cancel',
    }).then((result) => {
        if (result.isConfirmed) {
            // Make sure all hidden fields are properly set before submission
            ensureHiddenFieldsExist();
            submitBookingForm();
        }
    });
}

// Ensure all required hidden fields exist and have values
function ensureHiddenFieldsExist() {
    const bookingForm = document.getElementById('bookingForm');
    if (!bookingForm) return;

    // Check selectedBoat field
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input before submission');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        bookingForm.appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of previous value
    // This means the Tourism Office will handle boat arrangements for tourists
    selectedBoatInput.value = 'AssignedByTourismOffice';
    console.log('Setting boat to be assigned by tourism office before submission');

    // Check bookingId field
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input before submission');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        bookingForm.appendChild(newBookingIdInput);

        // Generate a booking ID for the new input
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        console.log('Generating new bookingId before submission');
        generateBookingId();
    }

    // Add duration field if it doesn't exist
    let durationInput = document.getElementById('duration');
    if (!durationInput) {
        console.log('Creating missing duration input before submission');
        durationInput = document.createElement('input');
        durationInput.type = 'hidden';
        durationInput.id = 'duration';
        durationInput.name = 'duration';
        bookingForm.appendChild(durationInput);

        // Calculate duration from start and end dates
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            durationInput.value = diffDays + ' day(s)';
            console.log('Setting duration value before submission:', durationInput.value);
        }
    }
}

// Comprehensive form validation before submission
function validateAllFields() {
    const fieldsToValidate = [
        { id: 'firstName', name: 'First Name' },
        { id: 'lastName', name: 'Last Name' },
        { id: 'age', name: 'Age' },
        { id: 'sex', name: 'Sex' },
        { id: 'contactNumber', name: 'Contact Number' },
        { id: 'city', name: 'City' },
        { id: 'province', name: 'Province' },
        { id: 'emergencyName', name: 'Emergency Contact Name' },
        { id: 'emergencyNumber', name: 'Emergency Number' }
    ];

    let firstInvalidField = null;
    let invalidFields = [];

    // Check each field
    fieldsToValidate.forEach(field => {
        const input = document.getElementById(field.id);
        if (input) {
            // Trigger validation
            validateField(input);

            // Check if field is invalid
            const invalidIcon = document.getElementById(field.id + '-invalid');
            if (invalidIcon && invalidIcon.classList.contains('show')) {
                invalidFields.push(field.name);
                if (!firstInvalidField) {
                    firstInvalidField = input;
                }
            }

            // Check if required field is empty
            if (input.hasAttribute('required') && input.value.trim() === '') {
                if (!invalidFields.includes(field.name)) {
                    invalidFields.push(field.name);
                }
                if (!firstInvalidField) {
                    firstInvalidField = input;
                }
            }
        }
    });

    // Check sex dropdown separately
    const sexSelect = document.getElementById('sex');
    if (sexSelect && sexSelect.value === '') {
        invalidFields.push('Sex');
        if (!firstInvalidField) {
            firstInvalidField = sexSelect;
        }
    }

    // If there are invalid fields, focus on first one and show alert
    if (invalidFields.length > 0) {
        if (firstInvalidField) {
            firstInvalidField.focus();
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        Swal.fire({
            title: 'Please Complete Required Fields',
            text: `Please fill in or correct the following field(s): ${invalidFields.join(', ')}`,
            icon: 'warning',
            confirmButtonText: 'OK',
            confirmButtonColor: '#00a8b5'
        });

        return false;
    }

    return true;
}

// Submit booking form
function submitBookingForm() {
    console.log('submitBookingForm() called');

    // Validate all fields first
    if (!validateAllFields()) {
        console.log('Form validation failed');
        return;
    }

    const bookingForm = document.getElementById('bookingForm');
    const formData = new FormData(bookingForm);

    // Add payment method
    const paymentRadio = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentRadio) {
        formData.append('paymentMethod', paymentRadio.value);
        console.log('Payment method added:', paymentRadio.value);

        // Update payment method in summary one last time before submission
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentRadio.value;
            console.log('Payment method updated in summary before submission:', paymentRadio.value);
        }
    } else {
        console.warn('No payment method selected!');
    }

    // Add total price
    const totalPriceElement = document.getElementById('totalPrice');
    if (totalPriceElement) {
        const totalPrice = totalPriceElement.textContent.replace('₱', '').replace(/,/g, '');
        formData.append('total', totalPrice);
        console.log('Total price added:', totalPrice);

        // Also add totalEnvironmentalFee as a backup
        const totalEnvFeeElement = document.getElementById('totalEnvironmentalFeeValue');
        if (totalEnvFeeElement) {
            formData.append('totalEnvironmentalFee', totalEnvFeeElement.value || totalPrice);
            console.log('Total environmental fee added:', totalEnvFeeElement.value || totalPrice);
        }
    } else {
        console.warn('Total price element not found!');
    }

    // Ensure current date is set for Today's Bookings
    const currentDateInput = document.getElementById('currentDate');
    if (!currentDateInput.value) {
        setCurrentDate();
        console.log('Current date set before submission:', currentDateInput.value);
    }

    // Set status
    formData.append('status', 'verification_pending');

    // Log all form data for debugging
    console.log('Form data being submitted:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    // Show loading state
    Swal.fire({
        title: 'Processing Booking...',
        text: 'Please wait while we process your booking.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
            console.log('Loading dialog shown');
        }
    });

    console.log('Sending request to integrated_verification.php...');

    // Send booking data to the integrated verification process
    fetch('../process/integrated_verification.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response received:', response);
        console.log('Response status:', response.status);

        // Always try to get the response as text first
        return response.text().then(text => {
            console.log('Response text:', text);

            // Try to parse as JSON if possible
            let data;
            try {
                data = JSON.parse(text);
                console.log('Successfully parsed JSON:', data);
                return data;
            } catch (e) {
                console.log('Not valid JSON, handling as text response');

                // Check if the text contains success indicators
                if (text.includes('success') || text.includes('successful') ||
                    text.includes('verification email sent') || text.includes('booking received')) {
                    console.log('Text indicates success');
                    return { success: true, message: 'Booking successful!' };
                }

                // Check if this is a PHP error
                if (text.includes('<br />') || text.includes('Fatal error') || text.includes('Warning')) {
                    console.error('PHP error detected in response');

                    // For debugging - log the full error
                    console.error('Full server error:', text);

                    // Return a success response anyway to allow the booking to be processed
                    console.log('Returning success despite PHP error to allow booking to continue');
                    return { success: true, message: 'Booking received! Note: There was an issue sending the confirmation email, but your booking has been processed.' };
                }

                // If response is empty or not recognized
                if (!text.trim()) {
                    console.error('Empty response from server');
                    // Return a success response anyway to allow the booking to be processed
                    return { success: true, message: 'Booking received! Note: There was an issue with the server response, but your booking has been processed.' };
                }

                // For any other unrecognized response
                console.error('Unrecognized server response');
                // Return a success response anyway to allow the booking to be processed
                return { success: true, message: 'Booking received! Note: There was an unexpected server response, but your booking has been processed.' };
            }
        });
    })
    .then(data => {
        console.log('Response data:', data);

        // Show success message and try to send email
        console.log('Showing success message and sending email');

        // Email is now sent directly from the integrated verification process
        if (data.success && data.booking_id) {
            console.log('Booking successful with ID:', data.booking_id);
        }

        Swal.fire({
            title: 'Booking Received!',
            text: 'Your booking has been received and a verification email has been sent to your email address. Please check your inbox (and spam folder) for confirmation details.',
            icon: 'success',
            confirmButtonText: 'OK'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();
            console.log('Form cleared and reset');
        });
    })
    .catch(error => {
        console.error('Error in fetch operation:', error);
        console.error('Error stack:', error.stack);

        // Log the error but still show a success message
        console.log('Showing success message despite error');

        Swal.fire({
            title: 'Booking Received!',
            text: 'Your booking has been received and a verification email has been sent to your email address. Please check your inbox (and spam folder) for confirmation details.',
            icon: 'success',
            confirmButtonText: 'OK'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();
            console.log('Form cleared and reset');
        });
    });
}

// Calculate total fees
function calculateTotalFees() {
    // Get values from inputs
    const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

    // Check total pax limit
    const totalPax = regularPax + discountedPax + childrenPax + infantsPax;
    if (totalPax > 25) {
        // Always show the alert when total exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The total number of passengers cannot exceed 25.',
            icon: 'warning',
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then(() => {
            // Reset to valid values after user acknowledges the alert
            document.getElementById('regularPax').value = Math.min(regularPax, 25);
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            // Removed numberOfPax field update

            // Recalculate with new values
            calculateTotalFees();
        });
        return;
    }

    // Calculate fees
    const regularFee = regularPax * 75;
    const discountedFee = discountedPax * 60;
    const childrenFee = childrenPax * 60;
    const infantsFee = 0; // Infants are free

    // Calculate total
    const totalFee = regularFee + discountedFee + childrenFee + infantsFee;

    // Update display
    document.getElementById('totalEnvironmentalFee').textContent = '₱' + totalFee.toFixed(2);
    document.getElementById('totalEnvironmentalFeeValue').value = totalFee;

    // Update summary
    document.getElementById('summaryEnvFeeValue').textContent = '₱' + totalFee.toFixed(2);

    // Update total price
    document.getElementById('totalPrice').textContent = '₱' + totalFee.toFixed(2);

    // Update hidden total input field
    document.getElementById('total').value = totalFee;
    console.log('Updated hidden total input field:', totalFee);

    // Update numberOfPax field to match total
    document.getElementById('numberOfPax').value = totalPax;

    // Update totalPassengers field to match environmental fees total
    const totalPassengersField = document.getElementById('totalPassengers');
    if (totalPassengersField && totalPax > 0) {
        totalPassengersField.value = totalPax;
    }

    // Update button states for all fee fields
    if (typeof updateFeeButtonStates === 'function') {
        ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'].forEach(function(fieldId) {
            updateFeeButtonStates(fieldId);
        });
    } else if (typeof window.updateFeeButtonStates === 'function') {
        ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'].forEach(function(fieldId) {
            window.updateFeeButtonStates(fieldId);
        });
    }
}

// Make function globally accessible
window.calculateTotalFees = calculateTotalFees;

// Sync number of pax
function syncNumberOfPax() {
    let numberOfPax = parseInt(document.getElementById('numberOfPax').value) || 0;

    // Enforce maximum limit
    if (numberOfPax > 25) {
        // Always show the alert when number exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The maximum number of passengers is 25.',
            icon: 'warning',
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then(() => {
            // Set to maximum allowed value after user acknowledges
            numberOfPax = 25;
            document.getElementById('numberOfPax').value = 25;

            // Update regular adults count
            document.getElementById('regularPax').value = numberOfPax;

            // Reset other passenger types
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            calculateTotalFees();
        });
        return;
    }

    // If number of pax is 0, set it to at least 1
    if (numberOfPax <= 0) {
        numberOfPax = 1;
        document.getElementById('numberOfPax').value = 1;
    }

    // Update regular adults count
    document.getElementById('regularPax').value = numberOfPax;

    // Reset other passenger types
    document.getElementById('discountedPax').value = 0;
    document.getElementById('childrenPax').value = 0;
    document.getElementById('infantsPax').value = 0;

    calculateTotalFees();
}

// Update date and time summary
function updateDateTimeSummary() {
    const startDate = startDatePicker.selectedDates[0];
    const endDate = endDatePicker.selectedDates[0];

    if (startDate) {
        const formattedStartDate = startDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('startDateTime').textContent = formattedStartDate;
        document.getElementById('summaryStartDate').textContent = formattedStartDate;
    }

    if (endDate) {
        const formattedEndDate = endDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('endDateTime').textContent = formattedEndDate;
        document.getElementById('summaryEndDate').textContent = formattedEndDate;
    }
}

// Update booking time
function updateBookingTime() {
    const now = new Date();
    const bookingTime = now.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true
    });
    document.getElementById('bookingTime').textContent = bookingTime;
    document.getElementById('summaryBookingTime').textContent = bookingTime;
}

// Update booking summary
function updateBookingSummary() {
    // Update personal information
    document.getElementById('summaryFirstName').textContent = document.getElementById('firstName').value || 'Not provided';
    document.getElementById('summaryLastName').textContent = document.getElementById('lastName').value || 'Not provided';
    document.getElementById('summaryAge').textContent = document.getElementById('age').value || 'Not provided';

    // Handle sex - simplified since we only have Male and Female options
    const sex = document.getElementById('sex').value;
    let sexText = sex || 'Not provided';
    document.getElementById('summarySex').textContent = sexText;

    // Update contact information
    document.getElementById('summaryContact').textContent = document.getElementById('contactNumber').value || 'Not provided';
    document.getElementById('summaryEmail').textContent = document.getElementById('emailAddress').value || 'Not provided';
    document.getElementById('summaryAddress').textContent = document.getElementById('completeAddress').value || 'Not provided';

    // Update emergency contact information
    const emergencyName = document.getElementById('emergencyName').value || '';
    const emergencyNumber = document.getElementById('emergencyNumber').value || '';
    let emergencyContactText = 'Not provided';
    if (emergencyName && emergencyNumber) {
        emergencyContactText = emergencyName + ' (' + emergencyNumber + ')';
    } else if (emergencyName) {
        emergencyContactText = emergencyName;
    } else if (emergencyNumber) {
        emergencyContactText = emergencyNumber;
    }
    document.getElementById('summaryEmergencyContact').textContent = emergencyContactText;

    // Update tour information
    document.getElementById('summaryDestination').textContent = document.getElementById('locationTourDestination').value || 'Not provided';
    document.getElementById('summaryDropOff').textContent = document.getElementById('dropOffLocation').value || 'Not provided';
    document.getElementById('summaryPax').textContent = document.getElementById('numberOfPax').value || 'Not provided';

    // Update payment method
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentMethod) {
        // Simply update the payment method value in the summary
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentMethod.value;
            console.log('Payment method updated in summary from updateBookingSummary:', paymentMethod.value);
        }
    }

    // Boat information removed - will be assigned by tourism office

    // Don't display booking ID in summary as it's controlled by admin
}

// Generate booking ID
function generateBookingId() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const random = Math.floor(10000 + Math.random() * 90000);
    const id = 'BOAT-' + yyyy + mm + dd + '-' + random;

    // Set the value in the hidden input only
    const bookingIdInput = document.getElementById('bookingId');
    if (bookingIdInput) {
        bookingIdInput.value = id;
    }

    // Don't display the booking ID in the form as it's controlled by admin
    // The booking ID is still generated and stored in the hidden input
    // for submission with the form

    return id;
}

// Set current date for Today's Bookings
function setCurrentDate() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const currentDate = yyyy + '-' + mm + '-' + dd;

    // Set the value in the hidden input
    const currentDateInput = document.getElementById('currentDate');
    if (currentDateInput) {
        currentDateInput.value = currentDate;
        console.log('Current date set for Today\'s Bookings:', currentDate);
    }
}

// Navigation functions
function nextStep(step) {
    // Validate current step
    if (!validateStep(step)) {
        return;
    }

    if (step >= totalSteps) {
        return;
    }

    // Update booking summary before navigating to payment step
    if (step + 1 === 3) {
        updateBookingSummary();
    }

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show next step
    document.getElementById('step' + (step + 1)).classList.add('active');

    // Update current step
    currentStep = step + 1;

    // Update progress
    updateProgress();

    // Save form data when moving to next step
    saveFormData();
}

function prevStep(step) {
    if (step <= 1) return;

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show previous step
    document.getElementById('step' + (step - 1)).classList.add('active');

    // Update current step
    currentStep = step - 1;

    // Update progress
    updateProgress();

    // Save form data when moving to previous step
    saveFormData();
}

// Update progress bar
function updateProgress() {
    const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
        progressBar.style.backgroundColor = progress === 100 ? '#28a745' : '#007bff';
    }

    // Update step indicators
    document.querySelectorAll('.step').forEach((step, index) => {
        const stepNumber = index + 1;
        if (stepNumber < currentStep) {
            step.classList.add('completed');
            step.classList.remove('active');
        } else if (stepNumber === currentStep) {
            step.classList.add('active');
            step.classList.remove('completed');
        } else {
            step.classList.remove('active', 'completed');
        }
    });
}

// Validate step
function validateStep(step) {
    console.log('Validating step:', step);

    switch (step) {
        case 1: // Personal Info
            console.log('Validating Personal Info step');
            const requiredFields = [
                { id: 'firstName', label: 'First Name' },
                { id: 'lastName', label: 'Last Name' },
                { id: 'age', label: 'Age' },
                { id: 'sex', label: 'Sex' },
                { id: 'contactNumber', label: 'Contact Number' },
                { id: 'emailAddress', label: 'Email Address' },
                { id: 'completeAddress', label: 'Complete Address' },
                { id: 'emergencyName', label: 'Emergency Contact Name' },
                { id: 'emergencyNumber', label: 'Emergency Contact Number' },
                { id: 'locationTourDestination', label: 'Tour Destination' },
                { id: 'dropOffLocation', label: 'Drop-off Location' },
                { id: 'numberOfPax', label: 'Number of Passengers' }
            ];

            for (let i = 0; i < requiredFields.length; i++) {
                const field = document.getElementById(requiredFields[i].id);
                if (!field || !field.value) {
                    console.log(`Missing required field: ${requiredFields[i].id}`);
                    Swal.fire({
                        title: 'Warning!',
                        text: `Please enter your ${requiredFields[i].label}.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        willClose: () => {
                            // This will run when the alert is about to close
                            setTimeout(() => {
                                if (field) {
                                    field.focus();
                                    field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }
                            }, 100); // Small delay to ensure the alert is fully closed
                        }
                    });
                    return false;
                }
            }

            // Email validation - now required
            const emailField = document.getElementById('emailAddress');
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailField || !emailField.value.trim()) {
                console.log('Email address is required');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Email address is required for booking confirmation.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        setTimeout(() => {
                            emailField?.focus();
                        }, 100);
                    }
                });
                return false;
            }
            if (!emailRegex.test(emailField.value)) {
                console.log('Invalid email format');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter a valid email address.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            emailField.focus();
                            emailField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }

            // Validate emergency contact (Dean's Emergency Control Mechanism)
            if (!validateEmergencyNumber(document.getElementById('emergencyNumber'))) {
                return false;
            }

            // Check if emergency contact is required for large groups
            if (!checkEmergencyContactRequired()) {
                return false;
            }

            // Validate companion information (main booker already validated above)
            const totalPassengers = parseInt(document.getElementById('totalPassengers').value) || 1;
            const companions = totalPassengers - 1; // Exclude main booker

            // Validate companions only (passenger2, passenger3, etc.)
            for (let i = 2; i <= totalPassengers; i++) {
                const additionalPassengerFields = [
                    { id: `passenger${i}FirstName`, label: `Additional Passenger ${i-1} First Name` },
                    { id: `passenger${i}LastName`, label: `Additional Passenger ${i-1} Last Name` },
                    { id: `passenger${i}Age`, label: `Additional Passenger ${i-1} Age` },
                    { id: `passenger${i}Sex`, label: `Additional Passenger ${i-1} Sex` },
                    { id: `passenger${i}City`, label: `Additional Passenger ${i-1} City` },
                    { id: `passenger${i}Province`, label: `Additional Passenger ${i-1} Province` }
                ];

                for (let j = 0; j < additionalPassengerFields.length; j++) {
                    const field = document.getElementById(additionalPassengerFields[j].id);
                    if (!field || !field.value.trim()) {
                        console.log(`Missing additional passenger field: ${additionalPassengerFields[j].id}`);
                        Swal.fire({
                            title: 'Warning!',
                            text: `Please enter ${additionalPassengerFields[j].label}.`,
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            willClose: () => {
                                setTimeout(() => {
                                    if (field) {
                                        field.focus();
                                        field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                    }
                                }, 100);
                            }
                        });
                        return false;
                    }
                }
            }

            // Check if environmental fee is filled
            const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
            const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
            const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
            const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

            console.log('Environmental fee values:', {
                regularPax,
                discountedPax,
                childrenPax,
                infantsPax
            });

            if (regularPax === 0 && discountedPax === 0 && childrenPax === 0 && infantsPax === 0) {
                console.log('No passengers entered in environmental fee section');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter at least one passenger in the Environmental Fee section.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        setTimeout(() => {
                            document.getElementById('regularPax').focus();
                            document.getElementById('regularPax').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                    }
                });
                return false;
            }

            console.log('Personal Info step validation passed');
            return true;

        case 2: // Date and Time
            console.log('Validating Date and Time step');

            // Check if date pickers are initialized
            if (!window.startDatePicker || !window.endDatePicker) {
                console.error('Date pickers not initialized properly');
                Swal.fire({
                    title: 'Error',
                    text: 'There was a problem with the date selection. Please refresh the page and try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return false;
            }

            // Get selected dates
            const startDateSelected = window.startDatePicker.selectedDates[0];
            const endDateSelected = window.endDatePicker.selectedDates[0];

            console.log('Selected dates:', {
                startDate: startDateSelected,
                endDate: endDateSelected
            });

            // Check if dates are selected
            if (!startDateSelected || !endDateSelected) {
                console.log('Missing date selection');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select both start and end dates.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            // Set focus on the appropriate date field
                            if (!startDateSelected) {
                                document.getElementById('startDate').focus();
                                document.getElementById('startDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            } else if (!endDateSelected) {
                                document.getElementById('endDate').focus();
                                document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }

            // Check if end date is after start date
            if (endDateSelected < startDateSelected) {
                console.log('End date is before start date');
                Swal.fire({
                    title: 'Warning!',
                    text: 'End date must be after start date.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        setTimeout(() => {
                            document.getElementById('endDate').focus();
                            document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                    }
                });
                return false;
            }

            console.log('Date and Time step validation passed');
            return true;

        case 3: // Payment
            const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
            if (!paymentMethod) {
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select a payment method.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            // Scroll to the payment methods section
                            document.querySelector('.payment-methods').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // Try to focus on the first payment option
                            const firstPaymentOption = document.getElementById('gcash');
                            if (firstPaymentOption) {
                                firstPaymentOption.focus();
                            }
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }
            return true;

        default:
            return true;
    }
}

// Reset booking steps
function resetBookingSteps() {
    // Remove .active from all steps
    document.querySelectorAll('.booking-step').forEach(step => step.classList.remove('active'));
    // Set Step 1 active
    document.getElementById('step1').classList.add('active');
    // Reset progress bar
    document.getElementById('progressBar').style.width = '0%';
    // Reset step indicators
    document.querySelectorAll('.step').forEach((step, i) => {
        if (i === 0) step.classList.add('active');
        else step.classList.remove('active');
    });
}

// Clear form data
function clearFormData() {
    document.getElementById('bookingForm').reset();
    resetBookingSteps();
    calculateTotalFees();
    updateDateTimeSummary();
    generateBookingId();
}

// Save form data to localStorage
function saveFormData() {
    console.log('Saving form data to localStorage...');

    // Ensure the selectedBoat input exists
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);

        // Set default value if empty
        if (!selectedBoatInput.value) {
            selectedBoatInput.value = 'DefaultBoat';
        }
    }

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);

        // Generate a booking ID
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        generateBookingId();
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    selectedBoatInput.value = 'AssignedByTourismOffice';

    const formData = {
        // Personal information
        firstName: document.getElementById('firstName')?.value || '',
        lastName: document.getElementById('lastName')?.value || '',
        age: document.getElementById('age')?.value || '',
        sex: document.getElementById('sex')?.value || '',
        contactNumber: document.getElementById('contactNumber')?.value || '',
        emailAddress: document.getElementById('emailAddress')?.value || '',
        city: document.getElementById('city')?.value || '',
        province: document.getElementById('province')?.value || '',
        completeAddress: document.getElementById('completeAddress')?.value || '',
        emergencyName: document.getElementById('emergencyName')?.value || '',
        emergencyNumber: document.getElementById('emergencyNumber')?.value || '',
        locationTourDestination: document.getElementById('locationTourDestination')?.value || '',
        dropOffLocation: document.getElementById('dropOffLocation')?.value || '',
        numberOfPax: document.getElementById('numberOfPax')?.value || '',

        // Environmental fees
        regularPax: document.getElementById('regularPax')?.value || '',
        discountedPax: document.getElementById('discountedPax')?.value || '',
        childrenPax: document.getElementById('childrenPax')?.value || '',
        infantsPax: document.getElementById('infantsPax')?.value || '',

        // Dates
        startDate: document.getElementById('startDate')?.value || '',
        endDate: document.getElementById('endDate')?.value || '',

        // Payment method
        payment: document.querySelector('input[name="paymentMethod"]:checked')?.value || '',

        // Boat information - always assigned by tourism office
        selectedBoat: 'AssignedByTourismOffice',
        bookingId: document.getElementById('bookingId')?.value || '',

        // Current step
        currentStep: currentStep,

        // Passenger data (additional passengers)
        totalPassengers: document.getElementById('totalPassengers')?.value || '',
        passengerData: getPassengerData(),

        // Flag to indicate user has entered data
        hasUserEnteredData: hasUserEnteredData()
    };

    console.log('Form data being saved');

    try {
        // Validate formData before saving
        if (!formData || typeof formData !== 'object') {
            console.error('Invalid form data object');
            return;
        }

        // Convert to JSON string
        const formDataString = JSON.stringify(formData);

        // Validate JSON string
        if (!formDataString || formDataString === 'undefined' || formDataString === 'null' || formDataString === '{}') {
            console.error('Invalid JSON string generated');
            return;
        }

        // Save to localStorage
        localStorage.setItem('bookingFormData', formDataString);
        console.log('Form data saved to localStorage successfully');
    } catch (error) {
        console.error('Error saving form data:', error);
        // Don't show an error to the user, just log it
    }

    // Boat information removed - will be assigned by tourism office

    const step1BookingId = document.getElementById('step1BookingId');
    if (step1BookingId && formData.bookingId) {
        step1BookingId.textContent = formData.bookingId;
    }

    // Boat information removed - will be assigned by tourism office

    const summaryBookingId = document.getElementById('summaryBookingId');
    if (summaryBookingId && formData.bookingId) {
        summaryBookingId.textContent = formData.bookingId;
    }
}

// Check if user has entered any meaningful data
function hasUserEnteredData() {
    // Check for user-entered data in important fields
    const firstName = document.getElementById('firstName')?.value || '';
    const lastName = document.getElementById('lastName')?.value || '';
    const email = document.getElementById('emailAddress')?.value || '';
    const phone = document.getElementById('contactNumber')?.value || '';
    const address = document.getElementById('completeAddress')?.value || '';
    const emergencyName = document.getElementById('emergencyName')?.value || '';
    const emergencyNumber = document.getElementById('emergencyNumber')?.value || '';
    const dropOffLocation = document.getElementById('dropOffLocation')?.value || '';
    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';

    // Return true if any important field has user data
    return firstName !== '' ||
           lastName !== '' ||
           email !== '' ||
           phone !== '' ||
           address !== '' ||
           emergencyName !== '' ||
           emergencyNumber !== '' ||
           dropOffLocation !== '' ||
           startDate !== '' ||
           endDate !== '';
}

// Load form data from localStorage
function loadFormData() {
    console.log('Loading form data from localStorage...');

    try {
        const savedData = localStorage.getItem('bookingFormData');
        if (!savedData || savedData === 'undefined' || savedData === 'null') {
            console.log('No valid saved form data found');
            return;
        }

        // Validate that the saved data is a proper JSON string
        if (typeof savedData !== 'string' || !savedData.startsWith('{')) {
            console.error('Invalid saved form data format');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsing saved form data');
        const formData = JSON.parse(savedData);

        // Validate that formData is an object
        if (!formData || typeof formData !== 'object') {
            console.error('Parsed form data is not an object');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsed form data successfully');

        // Ensure the form exists
        const bookingForm = document.getElementById('bookingForm');
        if (!bookingForm) {
            console.error('Booking form not found!');
            return;
        }

        // Fill personal information
        if (formData.firstName) {
            document.getElementById('firstName').value = formData.firstName;
            validateField(document.getElementById('firstName'));
        }
        if (formData.lastName) {
            document.getElementById('lastName').value = formData.lastName;
            validateField(document.getElementById('lastName'));
        }
        if (formData.age) {
            document.getElementById('age').value = formData.age;
            validateField(document.getElementById('age'));
        }
        if (formData.sex) {
            document.getElementById('sex').value = formData.sex;
            validateField(document.getElementById('sex'));
        }
        if (formData.contactNumber) {
            document.getElementById('contactNumber').value = formData.contactNumber;
            validateField(document.getElementById('contactNumber'));
        }
        if (formData.emailAddress) {
            document.getElementById('emailAddress').value = formData.emailAddress;
            validateField(document.getElementById('emailAddress'));
        }
        if (formData.city) {
            document.getElementById('city').value = formData.city;
            validateField(document.getElementById('city'));
        }
        if (formData.province) {
            document.getElementById('province').value = formData.province;
            validateField(document.getElementById('province'));
        }
        if (formData.completeAddress) document.getElementById('completeAddress').value = formData.completeAddress;
        if (formData.emergencyName) {
            document.getElementById('emergencyName').value = formData.emergencyName;
            validateField(document.getElementById('emergencyName'));
        }
        if (formData.emergencyNumber) {
            document.getElementById('emergencyNumber').value = formData.emergencyNumber;
            validateField(document.getElementById('emergencyNumber'));
        }
        if (formData.locationTourDestination) document.getElementById('locationTourDestination').value = formData.locationTourDestination;
        if (formData.dropOffLocation) document.getElementById('dropOffLocation').value = formData.dropOffLocation;
        if (formData.numberOfPax) document.getElementById('numberOfPax').value = formData.numberOfPax;

        // Fill environmental fees
        if (formData.regularPax) document.getElementById('regularPax').value = formData.regularPax;
        if (formData.discountedPax) document.getElementById('discountedPax').value = formData.discountedPax;
        if (formData.childrenPax) document.getElementById('childrenPax').value = formData.childrenPax;
        if (formData.infantsPax) document.getElementById('infantsPax').value = formData.infantsPax;

        // Fill dates
        if (formData.startDate) {
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.value = formData.startDate;
                // If we have flatpickr initialized, update it too
                if (window.startDatePicker) {
                    window.startDatePicker.setDate(formData.startDate);
                }
            }
        }

        if (formData.endDate) {
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.value = formData.endDate;
                // If we have flatpickr initialized, update it too
                if (window.endDatePicker) {
                    window.endDatePicker.setDate(formData.endDate);
                }
            }
        }

        // Select payment method
        if (formData.payment) {
            const paymentRadio = document.querySelector(`input[name="paymentMethod"][value="${formData.payment}"]`);
            if (paymentRadio) {
                paymentRadio.checked = true;

                // Show the appropriate payment details
                document.getElementById('gcashDetails').style.display = formData.payment === 'GCash' ? 'block' : 'none';
                document.getElementById('manualDetails').style.display = formData.payment === 'Manual Payment' ? 'block' : 'none';
            }
        }

        // Restore boat information
        // Make sure the selectedBoat input exists
        let selectedBoatInput = document.getElementById('selectedBoat');
        if (!selectedBoatInput) {
            console.log('Creating missing selectedBoat input during load');
            selectedBoatInput = document.createElement('input');
            selectedBoatInput.type = 'hidden';
            selectedBoatInput.id = 'selectedBoat';
            selectedBoatInput.name = 'selectedBoat';
            bookingForm.appendChild(selectedBoatInput);
        }

        // Always set to "AssignedByTourismOffice" regardless of localStorage or URL parameters
        console.log('Setting boat to be assigned by tourism office during form data load');
        selectedBoatInput.value = 'AssignedByTourismOffice';

        // Make sure the bookingId input exists
        const bookingIdInput = document.getElementById('bookingId');
        if (!bookingIdInput) {
            console.log('Creating missing bookingId input during load');
            const newBookingIdInput = document.createElement('input');
            newBookingIdInput.type = 'hidden';
            newBookingIdInput.id = 'bookingId';
            newBookingIdInput.name = 'bookingId';
            bookingForm.appendChild(newBookingIdInput);
        }

        // Set the value from localStorage if available, otherwise generate a new one
        if (formData.bookingId) {
            console.log('Setting bookingId from localStorage:', formData.bookingId);
            const existingBookingId = document.getElementById('bookingId');
            if (existingBookingId) {
                existingBookingId.value = formData.bookingId;
            }
        } else {
            console.log('Generating new bookingId');
            generateBookingId();
        }

        // Update calculations
        calculateTotalFees();
        updateDateTimeSummary();
        updateBookingSummary();


        // Don't display booking ID in the form as it's controlled by admin
        // The booking ID is still stored in the hidden input field

        // Go to the saved step
        if (formData.currentStep && formData.currentStep > 1) {
            console.log('Restoring to step:', formData.currentStep);
            // Hide current step
            const activeStep = document.querySelector('.booking-step.active');
            if (activeStep) {
                activeStep.classList.remove('active');
            }

            // Show saved step
            const targetStep = document.getElementById('step' + formData.currentStep);
            if (targetStep) {
                targetStep.classList.add('active');

                // Update current step
                currentStep = formData.currentStep;

                // Update progress
                updateProgress();
            } else {
                console.error('Target step not found:', 'step' + formData.currentStep);
            }
        }

        // Only show the notification if the user has actually entered data
        // Use the flag we saved that indicates user has entered data
        if (formData.hasUserEnteredData === true) {
            // Show a notification that data was restored
            Swal.fire({
                title: 'Form Data Restored',
                text: 'Your previously entered information has been restored.',
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }

        // Restore passenger data if available
        if (formData.totalPassengers) {
            // Set total passengers first
            const totalPassengersField = document.getElementById('totalPassengers');
            if (totalPassengersField) {
                totalPassengersField.value = formData.totalPassengers;
                // Regenerate passenger table
                updatePassengerForms();

                // Restore passenger data after table is generated
                setTimeout(() => {
                    if (formData.passengerData) {
                        restorePassengerData(formData.passengerData);
                    }
                }, 100);
            }
        }

        // Save the form data again to ensure everything is properly saved
        // This helps with any fields that might have been updated during the loading process
        setTimeout(saveFormData, 500);

        console.log('Form data successfully loaded from localStorage');
    } catch (error) {
        console.error('Error loading saved form data:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);

        // Clear potentially corrupted data
        localStorage.removeItem('bookingFormData');

        // Check if there's any user-entered data in the form already
        const hasFormData = hasUserEnteredData();

        // Only show error notification if there's no data in the form
        // This prevents showing the error when the form already has content
        if (!hasFormData) {
            // Show error notification
            Swal.fire({
                title: 'Notice',
                text: 'Starting with a fresh form.',
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }
    }
}

// Override the clearFormData function to also clear localStorage
const originalClearFormData = clearFormData;
clearFormData = function() {
    // Call the original function
    originalClearFormData();

    // Clear localStorage
    localStorage.removeItem('bookingFormData');
};

// Simple function to go directly to payment step
function goToPaymentStep() {
    // Basic date validation
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
    }

    // Show step 3 (payment)
    document.querySelector('.booking-step.active').classList.remove('active');
    document.getElementById('step3').classList.add('active');

    // Update progress bar
    document.getElementById('progressBar').style.width = '100%';

    // Update booking summary
    if (typeof updateBookingSummary === 'function') {
        updateBookingSummary();
    }
}

// Mobile menu functionality
function initializeMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Toggle the 'show' class on the navbar collapse
            navbarCollapse.classList.toggle('show');

            // Toggle the 'expanded' class on the toggler for styling
            this.classList.toggle('expanded');

            // Update aria-expanded attribute
            const expanded = navbarCollapse.classList.contains('show');
            this.setAttribute('aria-expanded', expanded);
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navbarToggler.contains(event.target) &&
                !navbarCollapse.contains(event.target) &&
                navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                navbarToggler.classList.remove('expanded');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking on a nav link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (navbarCollapse.classList.contains('show')) {
                    navbarCollapse.classList.remove('show');
                    navbarToggler.classList.remove('expanded');
                    navbarToggler.setAttribute('aria-expanded', 'false');
                }
            });
        });
    }
}

// Validate and proceed function
function validateAndProceed(step) {
    // Simply call the nextStep function directly
    // The validateStep function in booking.js will handle the validation
    nextStep(step);
}



// Initialize fee button states
function initializeFeeButtonStates() {
    const feeFields = ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'];
    feeFields.forEach(function(fieldId) {
        updateFeeButtonStates(fieldId);
    });

    // Also update after a short delay to ensure everything is loaded
    setTimeout(function() {
        feeFields.forEach(function(fieldId) {
            updateFeeButtonStates(fieldId);
        });
    }, 100);
}

// Initialize drop-off location validation
function initializeDropOffValidation() {
    const dropOffLocationInput = document.getElementById('dropOffLocation');
    if (dropOffLocationInput) {
        dropOffLocationInput.addEventListener('input', function(e) {
            // Remove any numbers from the input
            let value = e.target.value;
            let filteredValue = value.replace(/[0-9]/g, '');

            if (value !== filteredValue) {
                e.target.value = filteredValue;
                // Show a brief warning with helpful message
                e.target.style.borderColor = '#ff6b6b';
                e.target.style.backgroundColor = '#fff5f5';

                // Show tooltip-like message
                const warningMsg = document.createElement('div');
                warningMsg.style.cssText = `
                    position: absolute;
                    top: 100%;
                    left: 0;
                    background: #ff6b6b;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 1000;
                    margin-top: 2px;
                `;
                warningMsg.textContent = 'Numbers are not allowed. Use letters only (e.g., Carles Port)';

                e.target.parentNode.style.position = 'relative';
                e.target.parentNode.appendChild(warningMsg);

                setTimeout(function() {
                    e.target.style.borderColor = '';
                    e.target.style.backgroundColor = '';
                    if (warningMsg.parentNode) {
                        warningMsg.parentNode.removeChild(warningMsg);
                    }
                }, 3000);
            }
        });

        dropOffLocationInput.addEventListener('keypress', function(e) {
            // Prevent numbers from being typed
            const char = e.key || String.fromCharCode(e.which);
            if (/[0-9]/.test(char)) {
                e.preventDefault();
                // Show a brief warning with helpful message
                e.target.style.borderColor = '#ff6b6b';
                e.target.style.backgroundColor = '#fff5f5';

                // Show tooltip-like message
                const warningMsg = document.createElement('div');
                warningMsg.style.cssText = `
                    position: absolute;
                    top: 100%;
                    left: 0;
                    background: #ff6b6b;
                    color: white;
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 1000;
                    margin-top: 2px;
                `;
                warningMsg.textContent = 'Numbers are not allowed. Use letters only (e.g., Carles Port)';

                e.target.parentNode.style.position = 'relative';
                e.target.parentNode.appendChild(warningMsg);

                setTimeout(function() {
                    e.target.style.borderColor = '';
                    e.target.style.backgroundColor = '';
                    if (warningMsg.parentNode) {
                        warningMsg.parentNode.removeChild(warningMsg);
                    }
                }, 3000);
            }
        });
    }
}



// Test function for plus/minus buttons
function testPlusMinusButtons() {
    console.log('=== TESTING PLUS/MINUS BUTTONS ===');

    // Test increaseFeeCount
    console.log('Testing increaseFeeCount for regularPax...');
    increaseFeeCount('regularPax');

    // Test decreaseFeeCount
    console.log('Testing decreaseFeeCount for regularPax...');
    decreaseFeeCount('regularPax');

    // Test all fields
    const fields = ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'];
    fields.forEach(field => {
        console.log(`Testing ${field}...`);
        const input = document.getElementById(field);
        if (input) {
            console.log(`${field} current value: ${input.value}`);
            increaseFeeCount(field);
            console.log(`${field} after increase: ${input.value}`);
        } else {
            console.error(`${field} input not found!`);
        }
    });

    console.log('=== TEST COMPLETE ===');
}

// Real-time field validation function
function validateField(input) {
    const fieldId = input.id;
    const value = input.value.trim();
    const validIcon = document.getElementById(fieldId + '-valid');
    const invalidIcon = document.getElementById(fieldId + '-invalid');

    if (!validIcon || !invalidIcon) return;

    let isValid = false;

    // Handle empty fields
    if (value === '') {
        if (input.hasAttribute('required')) {
            // Required field is empty - show invalid
            isValid = false;
        } else {
            // Optional field is empty - hide both icons
            validIcon.classList.remove('show');
            invalidIcon.classList.remove('show');
            input.classList.remove('has-validation');
            return;
        }
    } else {
        // Field has value - validate based on field type and pattern
        switch(fieldId) {
            case 'firstName':
            case 'lastName':
            case 'emergencyName':
            case 'city':
            case 'province':
                isValid = /^[A-Za-z\s]+$/.test(value) && value.length >= 2;
                break;
            case 'age':
                const age = parseInt(value);
                isValid = age >= 1 && age <= 120;
                break;
            case 'contactNumber':
            case 'emergencyNumber':
                isValid = /^09[0-9]{9}$/.test(value);
                break;
            case 'emailAddress':
                // Email is now required
                if (value === '') {
                    isValid = false;
                } else {
                    isValid = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
                }
                break;
            case 'sex':
                isValid = value !== '';
                break;
            case 'completeAddress':
                // Optional field - if empty, don't show icons; if has content, validate it
                if (value === '') {
                    validIcon.classList.remove('show');
                    invalidIcon.classList.remove('show');
                    input.classList.remove('has-validation');
                    return;
                }
                // If has content, validate that it's reasonable (at least 5 characters)
                isValid = value.length >= 5;
                break;
            default:
                isValid = value.length > 0;
        }
    }

    // Show appropriate icon
    if (isValid) {
        validIcon.classList.add('show');
        invalidIcon.classList.remove('show');
    } else {
        validIcon.classList.remove('show');
        invalidIcon.classList.add('show');
    }

    // Add padding for icon
    input.classList.add('has-validation');
}

// Age validation function to prevent long numbers and letters
function validateAge(input) {
    let value = input.value;

    // Remove any non-numeric characters (including 'e', '+', '-', '.')
    value = value.replace(/[^0-9]/g, '');

    // Prevent leading zeros (except for single 0)
    if (value.length > 1 && value.charAt(0) === '0') {
        value = value.substring(1);
    }

    // Limit to 3 digits maximum
    if (value.length > 3) {
        value = value.substring(0, 3);
    }

    // Convert to number and check range
    const age = parseInt(value);
    if (age > 120) {
        value = '120';
    }

    // Update input value
    input.value = value;
}

// Passenger Listing Functions
function increasePassengerCount() {
    const input = document.getElementById('totalPassengers');
    if (!input) return;

    const currentValue = parseInt(input.value) || 1;
    const maxValue = parseInt(input.max) || 25;

    if (currentValue < maxValue) {
        input.value = currentValue + 1;
        updatePassengerForms();
    } else {
        // Dean's Control Mechanism - Alert when limit reached
        Swal.fire({
            title: 'Maximum Limit Reached',
            text: `Oops! Maximum ${maxValue} passengers allowed per booking.`,
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    }
}

function decreasePassengerCount() {
    const input = document.getElementById('totalPassengers');
    if (!input) return;

    const currentValue = parseInt(input.value) || 1;
    const minValue = parseInt(input.min) || 1;

    if (currentValue > minValue) {
        input.value = currentValue - 1;
        updatePassengerForms();
    } else {
        // Dean's Control Mechanism - Alert when minimum reached
        Swal.fire({
            title: 'Minimum Limit Reached',
            text: `Oops! Minimum ${minValue} passenger required per booking.`,
            icon: 'info',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    }
}

function updatePassengerForms() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers').value) || 1;

    // Generate table input (Dean's actual request)
    generatePassengerInputTable(totalPassengers);

    // Update the main numberOfPax field to match
    const numberOfPaxField = document.getElementById('numberOfPax');
    if (numberOfPaxField) {
        numberOfPaxField.value = totalPassengers;
    }

    // Sync with environmental fees if needed
    syncEnvironmentalFeesWithPassengers(totalPassengers);
}

// Generate passenger input table for COMPANIONS ONLY (Dean's ACTUAL request)
function generatePassengerInputTable(totalPassengers) {
    const container = document.getElementById('passengerInputContainer');
    if (!container) return;

    // Validate passenger limit (including main booker)
    if (totalPassengers > 25) {
        Swal.fire({
            title: 'Warning!',
            text: 'The maximum number of passengers is 25.',
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        document.getElementById('totalPassengers').value = 25;
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Calculate companions (total passengers - 1 main booker)
    const companions = totalPassengers - 1;

    if (companions <= 0) {
        // Only main booker, no additional passengers
        container.innerHTML = `
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: center;">
                <h5 style="color: #00a8b5; margin-bottom: 10px;">
                    <i class="fas fa-user" style="margin-right: 8px;"></i>Solo Traveler
                </h5>
                <p style="margin: 0; color: #666;">
                    Only the main booker (you) will be traveling. No other passengers to declare.
                </p>
            </div>
        `;
        return;
    }

    // Create table input HTML for COMPANIONS ONLY (Dean's "NAKA TABLE LANG SYA")
    let tableHTML = `
        <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 20px;">
            <h5 style="color: #00a8b5; margin-bottom: 15px; text-align: center;">
                <i class="fas fa-users" style="margin-right: 8px;"></i>Additional Passengers Table (${companions} additional passengers)
            </h5>
            <div style="margin-bottom: 15px; padding: 10px; background: #e8f4f8; border-left: 4px solid #17a2b8; border-radius: 4px;">
                <strong>Note:</strong> The main booker's information is already filled in the form above.
                This table is for your <strong>${companions} additional passenger(s)</strong> only.
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #00a8b5; color: white;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center; width: 50px;">No.</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 150px;">First Name *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 150px;">Last Name *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 80px;">Age *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 100px;">Gender *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 120px;">City *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 120px;">Province *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 130px;">Contact (Optional)</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 180px;">Street Address (Optional)</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // Generate table rows for COMPANIONS only (starting from passenger2)
    for (let i = 1; i <= companions; i++) {
        const passengerNumber = i + 1; // passenger2, passenger3, etc.
        const rowStyle = i % 2 === 0 ? 'background-color: #f9f9f9;' : '';

        tableHTML += `
            <tr style="${rowStyle}">
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">
                    ${i}<br><small style="color: #666;">(Additional)</small>
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}FirstName" name="passenger${passengerNumber}FirstName"
                           placeholder="First name" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'name')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}LastName" name="passenger${passengerNumber}LastName"
                           placeholder="Last name" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'name')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="number" id="passenger${passengerNumber}Age" name="passenger${passengerNumber}Age"
                           placeholder="Age" required min="1" max="120"
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           onblur="validateAge(this)">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <select id="passenger${passengerNumber}Sex" name="passenger${passengerNumber}Sex" required
                            style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;">
                        <option value="">Select</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}City" name="passenger${passengerNumber}City"
                           placeholder="City" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'city')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}Province" name="passenger${passengerNumber}Province"
                           placeholder="Province" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'province')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="tel" id="passenger${passengerNumber}ContactNumber" name="passenger${passengerNumber}ContactNumber"
                           placeholder="09XXXXXXXXX (optional)"
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           maxlength="11" pattern="09[0-9]{9}"
                           oninput="validateInputWithAlert(this, /[^0-9]/g, 'contact')"
                           onblur="validateContactNumber(this)">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}Address" name="passenger${passengerNumber}Address"
                           placeholder="Street address"
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;">
                </td>
            </tr>
        `;
    }

    tableHTML += `
                    </tbody>
                </table>
            </div>
            <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; text-align: center;">
                <strong style="color: #28a745;">✅ Additional Passengers Table Generated: ${companions} additional passenger(s) + 1 main booker = ${totalPassengers} total passengers</strong>
                <br><small style="color: #666;">Fill all required fields (*) for additional passengers before proceeding</small>
            </div>
        </div>
    `;

    container.innerHTML = tableHTML;

    // Add event listeners to save form data when passenger inputs change
    for (let i = 2; i <= totalPassengers; i++) {
        const fields = ['FirstName', 'LastName', 'Age', 'Sex', 'City', 'Province', 'ContactNumber', 'Address'];

        fields.forEach(fieldName => {
            const element = document.getElementById(`passenger${i}${fieldName}`);
            if (element) {
                element.addEventListener('input', saveFormData);
                element.addEventListener('change', saveFormData);
            }
        });
    }
}

// Get passenger data for saving to localStorage
function getPassengerData() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    const passengerData = {};

    // Save data for additional passengers (passenger2, passenger3, etc.)
    for (let i = 2; i <= totalPassengers; i++) {
        const fields = ['FirstName', 'LastName', 'Age', 'Sex', 'City', 'Province', 'ContactNumber', 'Address'];

        fields.forEach(fieldName => {
            const element = document.getElementById(`passenger${i}${fieldName}`);
            if (element) {
                passengerData[`passenger${i}${fieldName}`] = element.value;
            }
        });
    }

    return passengerData;
}

// Restore passenger data from localStorage
function restorePassengerData(passengerData) {
    if (!passengerData || typeof passengerData !== 'object') {
        return;
    }

    // Restore data for additional passengers
    Object.keys(passengerData).forEach(fieldName => {
        const element = document.getElementById(fieldName);
        if (element && passengerData[fieldName]) {
            element.value = passengerData[fieldName];
        }
    });
}

// Validation with SweetAlert notification (Dean's Control Mechanism)
function validateInputWithAlert(input, invalidPattern, fieldType) {
    const originalValue = input.value;
    const invalidChars = originalValue.match(invalidPattern);

    if (invalidChars && invalidChars.length > 0) {
        // Remove invalid characters
        input.value = originalValue.replace(invalidPattern, '');

        // Show SweetAlert notification
        let message = '';
        let invalidCharsList = [...new Set(invalidChars)].join(', ');

        switch (fieldType) {
            case 'name':
                message = `Oops! "${invalidCharsList}" are not valid for names. Only letters and spaces are allowed.`;
                break;
            case 'city':
                message = `Oops! "${invalidCharsList}" are not valid for city names. Only letters and spaces are allowed.`;
                break;
            case 'province':
                message = `Oops! "${invalidCharsList}" are not valid for province names. Only letters and spaces are allowed.`;
                break;
            case 'contact':
                message = `Oops! "${invalidCharsList}" are not valid for contact numbers. Only numbers are allowed.`;
                break;
            default:
                message = `Oops! "${invalidCharsList}" are not valid characters.`;
        }

        // Show compact SweetAlert
        Swal.fire({
            title: 'Invalid Input',
            text: message,
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    }

    // Save form data after validation
    saveFormData();
}

// Age Validation Control
function validateAge(input) {
    const age = parseInt(input.value);

    if (age < 1 || age > 120) {
        input.value = '';
        Swal.fire({
            title: 'Invalid Age',
            text: 'Oops! Age must be between 1 and 120 years old.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
        return false;
    }

    saveFormData();
    return true;
}

// Contact Number Format Control
function validateContactNumber(input) {
    const contact = input.value;

    if (contact && contact.length > 0) {
        if (!contact.startsWith('09')) {
            Swal.fire({
                title: 'Invalid Format',
                text: 'Oops! Contact number must start with "09".',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
            return false;
        }

        if (contact.length !== 11) {
            Swal.fire({
                title: 'Invalid Length',
                text: 'Oops! Contact number must be exactly 11 digits long.',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
            return false;
        }
    }

    saveFormData();
    return true;
}

// Duplicate Name Detection Control
function checkDuplicatePassengers() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    const passengers = [];

    // Collect main booker data
    const mainFirstName = document.getElementById('firstName')?.value?.trim().toLowerCase();
    const mainLastName = document.getElementById('lastName')?.value?.trim().toLowerCase();
    if (mainFirstName && mainLastName) {
        passengers.push({
            name: `${mainFirstName} ${mainLastName}`,
            type: 'Main Booker',
            index: 0
        });
    }

    // Collect additional passengers data
    for (let i = 2; i <= totalPassengers; i++) {
        const firstName = document.getElementById(`passenger${i}FirstName`)?.value?.trim().toLowerCase();
        const lastName = document.getElementById(`passenger${i}LastName`)?.value?.trim().toLowerCase();
        if (firstName && lastName) {
            passengers.push({
                name: `${firstName} ${lastName}`,
                type: 'Additional Passenger',
                index: i
            });
        }
    }

    // Check for duplicates
    const duplicates = [];
    for (let i = 0; i < passengers.length; i++) {
        for (let j = i + 1; j < passengers.length; j++) {
            if (passengers[i].name === passengers[j].name) {
                duplicates.push({
                    name: passengers[i].name,
                    positions: [passengers[i], passengers[j]]
                });
            }
        }
    }

    if (duplicates.length > 0) {
        const duplicateNames = duplicates.map(d => d.name).join(', ');
        Swal.fire({
            title: 'Duplicate Passengers Found',
            text: `Oops! Duplicate passenger(s) detected: "${duplicateNames}". Please check for duplicates.`,
            icon: 'warning',
            confirmButtonText: 'OK'
        });
        return false;
    }

    return true;
}

// Environmental Fees vs Passengers Control
function validateEnvironmentalFees() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    const regularPax = parseInt(document.getElementById('regularPax')?.value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax')?.value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax')?.value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax')?.value) || 0;

    const totalFees = regularPax + discountedPax + childrenPax + infantsPax;

    // Only show warning if there's a significant mismatch and user is trying to proceed
    // Don't show warnings during normal +/- button operations
    if (totalFees !== totalPassengers && totalFees > 0) {
        console.log(`Fee validation: Environmental fees (${totalFees}) vs Total passengers (${totalPassengers})`);

        // Give a brief moment for synchronization to complete
        setTimeout(() => {
            const updatedTotalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
            const updatedRegularPax = parseInt(document.getElementById('regularPax')?.value) || 0;
            const updatedDiscountedPax = parseInt(document.getElementById('discountedPax')?.value) || 0;
            const updatedChildrenPax = parseInt(document.getElementById('childrenPax')?.value) || 0;
            const updatedInfantsPax = parseInt(document.getElementById('infantsPax')?.value) || 0;
            const updatedTotalFees = updatedRegularPax + updatedDiscountedPax + updatedChildrenPax + updatedInfantsPax;

            // Only show warning if still mismatched after sync
            if (updatedTotalFees !== updatedTotalPassengers && updatedTotalFees > 0) {
                Swal.fire({
                    title: 'Fee Mismatch',
                    text: `Environmental fees (${updatedTotalFees}) don't match total passengers (${updatedTotalPassengers}). Please adjust the fees.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Auto-Fix',
                    cancelButtonText: 'Manual Fix'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Auto-fix by setting all to regular pax
                        document.getElementById('regularPax').value = updatedTotalPassengers;
                        document.getElementById('discountedPax').value = 0;
                        document.getElementById('childrenPax').value = 0;
                        document.getElementById('infantsPax').value = 0;
                        calculateTotalFees();
                        saveFormData();
                    }
                });
                return false;
            }
        }, 100); // Small delay to allow sync to complete
    }

    return true;
}

// Date Range Validation Control
function validateBookingDates() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const today = new Date().toISOString().split('T')[0];

    if (startDate && startDate < today) {
        Swal.fire({
            title: 'Invalid Start Date',
            text: 'Oops! Start date cannot be in the past.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
        return false;
    }

    if (startDate && endDate && endDate < startDate) {
        Swal.fire({
            title: 'Invalid Date Range',
            text: 'Oops! End date cannot be before start date.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
        return false;
    }

    return true;
}

// Emergency Number Validation Control
function validateEmergencyNumber(input) {
    const emergencyNumber = input.value;

    if (emergencyNumber && emergencyNumber.length > 0) {
        // Check if it's a valid Philippine number format
        if (!emergencyNumber.startsWith('09') && !emergencyNumber.startsWith('+63')) {
            Swal.fire({
                title: 'Invalid Emergency Number',
                text: 'Oops! Emergency number must be a valid Philippine number (09XXXXXXXXX or +639XXXXXXXXX).',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
            return false;
        }

        // Check length for 09 format
        if (emergencyNumber.startsWith('09') && emergencyNumber.length !== 11) {
            Swal.fire({
                title: 'Invalid Emergency Number Length',
                text: 'Oops! Emergency number with 09 format must be exactly 11 digits long.',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
            return false;
        }

        // Check length for +63 format
        if (emergencyNumber.startsWith('+63') && emergencyNumber.length !== 13) {
            Swal.fire({
                title: 'Invalid Emergency Number Length',
                text: 'Oops! Emergency number with +63 format must be exactly 13 characters long.',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
            return false;
        }

        // Check if emergency number is same as main contact
        const mainContact = document.getElementById('contactNumber')?.value;
        if (mainContact && emergencyNumber === mainContact) {
            Swal.fire({
                title: 'Same as Main Contact',
                text: 'Oops! Emergency contact should be different from your main contact number for safety purposes.',
                icon: 'warning',
                confirmButtonText: 'OK'
            });
            return false;
        }

        // Success message for valid emergency number
        Swal.fire({
            title: 'Emergency Contact Verified',
            text: 'Emergency contact number is valid and ready for use.',
            icon: 'success',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true
        });
    }

    saveFormData();
    return true;
}

// Emergency Contact Required Check
function checkEmergencyContactRequired() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    const emergencyNumber = document.getElementById('emergencyNumber')?.value;

    // Require emergency contact for groups of 5 or more
    if (totalPassengers >= 5 && (!emergencyNumber || emergencyNumber.trim() === '')) {
        Swal.fire({
            title: 'Emergency Contact Required',
            text: `Oops! For safety purposes, groups of ${totalPassengers} passengers require an emergency contact number.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Add Emergency Contact',
            cancelButtonText: 'Skip for Now'
        }).then((result) => {
            if (result.isConfirmed) {
                // Focus on emergency contact field
                const emergencyField = document.getElementById('emergencyNumber');
                if (emergencyField) {
                    emergencyField.focus();
                    emergencyField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
        return false;
    }

    return true;
}

// Form Progress Control
function showFormProgress() {
    const requiredFields = [
        'firstName', 'lastName', 'age', 'sex', 'contactNumber',
        'city', 'province', 'startDate', 'endDate', 'locationTourDestination'
    ];

    let completedFields = 0;
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field && field.value.trim()) {
            completedFields++;
        }
    });

    // Check passenger fields
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    let passengerFieldsCompleted = 0;
    let totalPassengerFields = 0;

    for (let i = 2; i <= totalPassengers; i++) {
        const passengerRequiredFields = ['FirstName', 'LastName', 'Age', 'Sex', 'City', 'Province'];
        passengerRequiredFields.forEach(fieldName => {
            totalPassengerFields++;
            const field = document.getElementById(`passenger${i}${fieldName}`);
            if (field && field.value.trim()) {
                passengerFieldsCompleted++;
            }
        });
    }

    const totalRequired = requiredFields.length + totalPassengerFields;
    const totalCompleted = completedFields + passengerFieldsCompleted;
    const percentage = Math.round((totalCompleted / totalRequired) * 100);

    // Update progress indicator if exists
    const progressIndicator = document.getElementById('formProgress');
    if (progressIndicator) {
        progressIndicator.innerHTML = `
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; border-radius: 5px; padding: 10px; margin: 10px 0;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: bold; color: #00a8b5;">Form Progress:</span>
                    <span style="color: #666;">${totalCompleted}/${totalRequired} fields (${percentage}%)</span>
                </div>
                <div style="background: #e0f7fa; border-radius: 10px; height: 8px; margin-top: 5px;">
                    <div style="background: #00a8b5; height: 8px; border-radius: 10px; width: ${percentage}%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `;
    }

    return { completed: totalCompleted, total: totalRequired, percentage };
}

// Session Timeout Control
let sessionTimer;
let sessionWarningShown = false;

function initSessionTimeout() {
    const sessionDuration = 30 * 60 * 1000; // 30 minutes
    const warningTime = 5 * 60 * 1000; // 5 minutes before expiry

    // Clear existing timer
    if (sessionTimer) {
        clearTimeout(sessionTimer);
    }

    // Set warning timer
    sessionTimer = setTimeout(() => {
        if (!sessionWarningShown) {
            sessionWarningShown = true;
            Swal.fire({
                title: 'Session Expiring Soon',
                text: 'Oops! Your session will expire in 5 minutes. Please save your progress.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Extend Session',
                cancelButtonText: 'Continue Working'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Reset session
                    sessionWarningShown = false;
                    initSessionTimeout();
                    saveFormData();
                }
            });
        }
    }, sessionDuration - warningTime);
}

// Internet Connection Control
function checkConnection() {
    if (!navigator.onLine) {
        Swal.fire({
            title: 'No Internet Connection',
            text: 'Oops! No internet connection detected. Your data is saved locally and will sync when reconnected.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true
        });
        return false;
    }
    return true;
}

// Connection status monitoring
window.addEventListener('online', () => {
    Swal.fire({
        title: 'Connection Restored',
        text: 'Internet connection restored. Syncing data...',
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true
    });
    saveFormData(); // Sync data when back online
});

window.addEventListener('offline', () => {
    checkConnection();
});

// Initialize all control mechanisms when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize session timeout
    initSessionTimeout();

    // Check initial connection
    checkConnection();

    // Show initial form progress
    showFormProgress();

    // Add event listeners for date validation
    const startDateField = document.getElementById('startDate');
    const endDateField = document.getElementById('endDate');

    if (startDateField) {
        startDateField.addEventListener('change', validateBookingDates);
    }

    if (endDateField) {
        endDateField.addEventListener('change', validateBookingDates);
    }

    // Add event listeners for environmental fees validation
    const feeFields = ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'];
    feeFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('change', validateEnvironmentalFees);
        }
    });

    // Add event listeners for emergency contact validation
    const emergencyNumberField = document.getElementById('emergencyNumber');
    if (emergencyNumberField) {
        emergencyNumberField.addEventListener('blur', validateEmergencyNumber);
        emergencyNumberField.addEventListener('input', (e) => {
            // Only allow numbers, +, and spaces for emergency number
            e.target.value = e.target.value.replace(/[^0-9+\s]/g, '');
        });
    }

    const emergencyNameField = document.getElementById('emergencyName');
    if (emergencyNameField) {
        emergencyNameField.addEventListener('input', (e) => {
            // Only allow letters and spaces for emergency name
            e.target.value = e.target.value.replace(/[^A-Za-z\s]/g, '');
        });
    }

    // Check emergency contact requirement when passenger count changes
    const totalPassengersField = document.getElementById('totalPassengers');
    if (totalPassengersField) {
        totalPassengersField.addEventListener('change', () => {
            setTimeout(checkEmergencyContactRequired, 500);
        });
    }

    // Add event listeners for form progress tracking
    const allInputs = document.querySelectorAll('input, select, textarea');
    allInputs.forEach(input => {
        input.addEventListener('input', () => {
            setTimeout(showFormProgress, 100); // Small delay to ensure value is updated
        });
        input.addEventListener('change', () => {
            setTimeout(showFormProgress, 100);
        });
    });

    // Add duplicate check on passenger name changes
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    for (let i = 2; i <= 25; i++) { // Check up to max passengers
        const firstNameField = document.getElementById(`passenger${i}FirstName`);
        const lastNameField = document.getElementById(`passenger${i}LastName`);

        if (firstNameField) {
            firstNameField.addEventListener('blur', checkDuplicatePassengers);
        }
        if (lastNameField) {
            lastNameField.addEventListener('blur', checkDuplicatePassengers);
        }
    }

    // Reset session timer on user activity
    ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, () => {
            if (sessionWarningShown) {
                sessionWarningShown = false;
                initSessionTimeout();
            }
        });
    });

    console.log('All Dean\'s Control Mechanisms initialized successfully! 🎯');
});



// Sync environmental fees with passenger count
function syncEnvironmentalFeesWithPassengers(totalPassengers) {
    console.log(`Syncing environmental fees with total passengers: ${totalPassengers}`);

    // Get current environmental fees
    const regularPax = parseInt(document.getElementById('regularPax')?.value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax')?.value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax')?.value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax')?.value) || 0;

    const currentFeesTotal = regularPax + discountedPax + childrenPax + infantsPax;

    // Only auto-adjust if environmental fees are currently 0 or don't match
    if (currentFeesTotal === 0 || currentFeesTotal !== totalPassengers) {
        // Set all passengers as regular adults by default
        // User can manually adjust the breakdown later
        document.getElementById('regularPax').value = totalPassengers;
        document.getElementById('discountedPax').value = 0;
        document.getElementById('childrenPax').value = 0;
        document.getElementById('infantsPax').value = 0;

        // Recalculate fees
        if (typeof window.calculateTotalFees === 'function') {
            window.calculateTotalFees();
        }

        console.log(`Auto-adjusted environmental fees to match ${totalPassengers} passengers`);
    }
}

// Update totalPassengers field based on environmental fees total
function updateTotalPassengersFromFees() {
    const regularPax = parseInt(document.getElementById('regularPax')?.value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax')?.value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax')?.value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax')?.value) || 0;

    const totalFromFees = regularPax + discountedPax + childrenPax + infantsPax;

    // Update totalPassengers field
    const totalPassengersField = document.getElementById('totalPassengers');
    if (totalPassengersField && totalFromFees > 0) {
        totalPassengersField.value = totalFromFees;
        console.log(`Updated totalPassengers to ${totalFromFees} based on environmental fees`);

        // Update passenger forms to match
        if (typeof window.generatePassengerInputTable === 'function') {
            window.generatePassengerInputTable(totalFromFees);
        }
    }
}

// Initialize passenger forms on page load
function initializePassengerForms() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updatePassengerForms);
    } else {
        updatePassengerForms();
    }
}

// Make functions available globally
window.nextStep = nextStep;
window.prevStep = prevStep;
window.calculateTotalFees = calculateTotalFees;
window.clearFormData = clearFormData;
window.goToPaymentStep = goToPaymentStep;
window.validateAndProceed = validateAndProceed;
window.increaseFeeCount = increaseFeeCount;
window.decreaseFeeCount = decreaseFeeCount;
window.updateFeeButtonStates = updateFeeButtonStates;
window.testPlusMinusButtons = testPlusMinusButtons;

// Passenger Listing Functions
window.increasePassengerCount = increasePassengerCount;
window.decreasePassengerCount = decreasePassengerCount;
window.updatePassengerForms = updatePassengerForms;
window.syncEnvironmentalFeesWithPassengers = syncEnvironmentalFeesWithPassengers;
window.updateTotalPassengersFromFees = updateTotalPassengersFromFees;

// Passenger Table Input Functions (Dean's ACTUAL Request)
window.generatePassengerInputTable = generatePassengerInputTable;

// Validation Alert Functions (Dean's Control Mechanism)
window.validateInputWithAlert = validateInputWithAlert;
window.validateAge = validateAge;
window.validateContactNumber = validateContactNumber;
window.validateEmergencyNumber = validateEmergencyNumber;
window.checkEmergencyContactRequired = checkEmergencyContactRequired;
window.checkDuplicatePassengers = checkDuplicatePassengers;
window.validateEnvironmentalFees = validateEnvironmentalFees;
window.validateBookingDates = validateBookingDates;

// Validation Alert Functions (Dean's Control Mechanism)
window.showValidationToast = showValidationToast;
window.validateTextInput = validateTextInput;
window.validateNumberInput = validateNumberInput;




