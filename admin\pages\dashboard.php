<?php
/**
 * Admin Dashboard
 *
 * Main dashboard for the admin system
 */

// Production error handling
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

// Define admin system constants (only if not already defined)
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}
if (!defined('ADMIN_BASE_URL')) {
    define('ADMIN_BASE_URL', '/Online Booking Reservation System/admin/');
}

// Include admin configuration
require_once ADMIN_ROOT . 'includes/config.php';
checkLogin();

// Get admin details
$admin = getAdminDetails($_SESSION['admin_id']);

// Update last_activity for admin on each page load
if(isset($_SESSION['admin_id'])) {
    try {
        $admin_id = intval($_SESSION['admin_id']);
        // Set a shorter timeout for this query to prevent lock issues
        $con->query("SET SESSION lock_wait_timeout=5");
        $stmt = $con->prepare("UPDATE admins SET last_activity=NOW() WHERE admin_id=?");
        $stmt->bind_param("i", $admin_id);
        // Use a timeout to prevent hanging
        $stmt->execute();
    } catch (Exception $e) {
        // Log the error but continue - this update is not critical
        error_log("Failed to update admin last_activity: " . $e->getMessage());
        // Continue with the page load even if this fails
    }
}

// Check for login success message
$login_success = '';
if (isset($_SESSION['login_success'])) {
    $login_success = $_SESSION['login_success'];
    unset($_SESSION['login_success']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Online Booking System | Carles Tourism</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/fontawesome-free/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!--  Bootstrap -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
  <!-- iCheck -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
  <!-- JQVMap -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/jqvmap/jqvmap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/dist/css/adminlte.min.css">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/overlayScrollbars/css/OverlayScrollbars.min.css">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/daterangepicker/daterangepicker.css">
  <!-- summernote -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/summernote/summernote-bs4.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>pages/plugins/sweetalert2/sweetalert2.min.css">

  <!-- Sidebar fix -->
  <link rel="stylesheet" href="css/sidebar-fix.css">

  <!-- Dashboard custom styles -->
  <style>
    #no-internet-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      display: none;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
    }

    /* Custom colors for passenger manifest stats */
    .bg-teal {
      background-color: #20c997 !important;
    }
    .bg-purple {
      background-color: #6f42c1 !important;
    }
    .bg-orange {
      background-color: #fd7e14 !important;
    }

    /* Improve stat box hover effects */
    .small-box:hover {
      transform: translateY(-2px);
      transition: transform 0.2s ease;
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    /* Better spacing for dashboard sections */
    .dashboard-section-title {
      margin: 30px 0 20px 0;
      padding: 15px 0;
      border-bottom: 2px solid #dee2e6;
      color: #495057;
      font-weight: 600;
    }

    /* Quick action buttons styling */
    .btn-lg {
      padding: 15px 10px;
      font-size: 0.9rem;
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    .btn-lg:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }

    .btn-lg i {
      font-size: 1.5rem;
      margin-bottom: 5px;
    }

    .btn-lg small {
      font-size: 0.75rem;
      font-weight: 500;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
      .dashboard-section-title {
        font-size: 1.1rem;
        margin: 20px 0 15px 0;
      }

      .btn-lg {
        padding: 12px 8px;
        font-size: 0.8rem;
      }

      .btn-lg i {
        font-size: 1.2rem;
      }

      .small-box .inner h3 {
        font-size: 1.5rem;
      }
    }

    /* Welcome card styling */
    .bg-gradient-primary {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    /* About system card styling */
    .bg-gradient-info {
      background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    /* Card improvements */
    .card {
      border: none;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
      transition: box-shadow 0.3s ease;
    }
  </style>
</head>
<?php
// Determine user role
$admin_id = intval($_SESSION['aid']);
$stmt = $con->prepare("SELECT role FROM admins WHERE admin_id = ?");
$stmt->bind_param("i", $admin_id);
$stmt->execute();
$result = $stmt->get_result();
$admin_data = $result->fetch_assoc();
$user_role = 'admin'; // Subadmin functionality has been removed


?>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Navbar -->
  <?php include_once(ADMIN_ROOT . 'includes/navbar.php');?>

  <!-- Main Sidebar Container -->
  <?php include_once(ADMIN_ROOT . 'includes/sidebar.php');?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0"></h1>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <!-- Breadcrumb removed as requested -->
          </div><!-- /.col -->
        </div><!-- /.row -->

        <?php if (!empty($login_success)): ?>
        <!-- Login Success Message -->
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle mr-2"></i> <?php echo htmlspecialchars($login_success); ?>
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <?php endif; ?>

      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">

        <!-- Welcome Message -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card bg-gradient-primary text-white">
              <div class="card-body">
                <div class="row align-items-center">
                  <div class="col-md-8">
                    <h4 class="mb-2">
                      <i class="fas fa-tachometer-alt mr-2"></i>
                      Welcome to the Admin Dashboard
                    </h4>
                    <p class="mb-0">
                      Monitor your booking system, manage reservations, and track passenger manifests all in one place.
                      <br><small>Last updated: <?php echo date('F j, Y g:i A'); ?></small>
                    </p>
                  </div>
                  <div class="col-md-4 text-right">
                    <i class="fas fa-ship" style="font-size: 4rem; opacity: 0.3;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Calculate stats for dashboard -->
        <?php
        // Dashboard statistics calculation

        // Initialize variables with default values
        $todaysBookings = 0;
        $totalReservations = 0;
        $rejectedbookings = 0;
        $newbookings = 0;
        $acceptedbookings = 0;
        $allMonths = [];
        $allCounts = [];

        try {
            // Check if bookings table exists
            $check_table_sql = "SHOW TABLES LIKE 'bookings'";
            $table_result = $con->query($check_table_sql);

            if ($table_result->num_rows > 0) {
                // Get today's date
                $today = date('Y-m-d');

                // Check if is_today_booking column exists
                $check_column = $con->query("SHOW COLUMNS FROM bookings LIKE 'is_today_booking'");
                $has_today_flag = $check_column->num_rows > 0;



                // Count only bookings for today
                $today = date('Y-m-d');

                // Check if is_today_booking column exists
                $check_column = $con->query("SHOW COLUMNS FROM bookings LIKE 'is_today_booking'");
                $has_today_flag = $check_column->num_rows > 0;

                if ($has_today_flag) {
                    // If the column exists, use it as the primary filter
                    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE is_today_booking = 1 OR DATE(created_at) = ? OR DATE(start_date) = ?");
                    $stmt->bind_param("ss", $today, $today);
                } else {
                    // Otherwise, use only date fields
                    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE DATE(created_at) = ? OR DATE(start_date) = ?");
                    $stmt->bind_param("ss", $today, $today);
                }

                $stmt->execute();
                $result = $stmt->get_result();
                $todaysBookings = $result->fetch_row()[0];

                // Total Reservations - Count from bookings table for consistency
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings");
                $stmt->execute();
                $totalReservations = $stmt->get_result()->fetch_row()[0];

                // Count cancelled bookings from the bookings table (same as rejected-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status='cancelled'");
                $stmt->execute();
                $rejectedbookings = $stmt->get_result()->fetch_row()[0];

                // New Bookings - Count from bookings table (same as new-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status='pending'");
                $stmt->execute();
                $newbookings = $stmt->get_result()->fetch_row()[0];

                // Confirmed Bookings - Count both 'confirmed' and 'accepted' status (same as accepted-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status IN ('confirmed', 'accepted')");
                $stmt->execute();
                $acceptedbookings = $stmt->get_result()->fetch_row()[0];

                // Passenger Manifest Statistics
                $totalPassengers = 0;
                $manifestCompletionRate = 0;

                // Check if passenger_manifest table exists
                $check_manifest_table = $con->query("SHOW TABLES LIKE 'passenger_manifest'");
                if ($check_manifest_table->num_rows > 0) {
                    // Total passengers from manifest
                    $stmt = $con->prepare("SELECT COUNT(*) FROM passenger_manifest");
                    $stmt->execute();
                    $totalPassengers = $stmt->get_result()->fetch_row()[0];

                    // Calculate manifest completion rate
                    if ($totalReservations > 0) {
                        $stmt = $con->prepare("SELECT COUNT(DISTINCT booking_id) FROM passenger_manifest");
                        $stmt->execute();
                        $bookingsWithManifest = $stmt->get_result()->fetch_row()[0];
                        $manifestCompletionRate = round(($bookingsWithManifest / $totalReservations) * 100, 1);
                    }
                }

                // Monthly data for charts
                $stmt = $con->prepare("SELECT DATE_FORMAT(start_date, '%b %Y') as month, COUNT(*) as count FROM bookings GROUP BY month ORDER BY MIN(start_date) ASC");
                $stmt->execute();
                $result = $stmt->get_result();

                while($row = $result->fetch_assoc()) {
                    $allMonths[] = $row['month'];
                    $allCounts[] = $row['count'];
                }
            }
        } catch (Exception $e) {
            // Log the error but don't display it to the user
            error_log("Error in dashboard.php stats: " . $e->getMessage());
        }

        // Subadmin functionality has been removed

        // Ensure 'May' is present for current year
        $mayLabel = 'May ' . date('Y');
        if (!in_array($mayLabel, $allMonths)) {
            $allMonths[] = $mayLabel;
            $allCounts[] = 0;
        }

        $months = array_slice($allMonths, -6);
        $monthlyCounts = array_slice($allCounts, -6);
        ?>

        <!-- BOOKING STATISTICS SECTION -->
        <h4 class="dashboard-section-title">
          <i class="fas fa-chart-bar"></i> Booking Statistics
        </h4>
        <div class="row justify-content-center">
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-info">
              <div class="inner text-center">
                <h3><?php echo $todaysBookings; ?></h3>
                <p>Today's Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-calendar-day"></i>
              </div>
              <a href="all-booking.php?filter=today" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-success">
              <div class="inner text-center">
                <h3><?php echo $totalReservations; ?></h3>
                <p>Total Reservations</p>
              </div>
              <div class="icon">
                <i class="fas fa-list-alt"></i>
              </div>
              <a href="all-booking.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-warning">
              <div class="inner text-center">
                <h3><?php echo $newbookings; ?></h3>
                <p>New Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-bookmark"></i>
              </div>
              <a href="new-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-success">
              <div class="inner text-center">
                <h3><?php echo $acceptedbookings; ?></h3>
                <p>Confirmed Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <a href="accepted-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-danger">
              <div class="inner text-center">
                <h3><?php echo $rejectedbookings; ?></h3>
                <p>Rejected Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-times-circle"></i>
              </div>
              <a href="rejected-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

        </div>
        <!-- END: Booking Stat Boxes -->

        <!-- PASSENGER MANIFEST SECTION -->
        <h4 class="dashboard-section-title">
          <i class="fas fa-users"></i> Passenger Manifest Statistics
        </h4>
        <div class="row justify-content-center">
          <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="small-box bg-primary">
              <div class="inner text-center">
                <h3><?php echo $totalPassengers; ?></h3>
                <p>Total Passengers</p>
              </div>
              <div class="icon">
                <i class="fas fa-users"></i>
              </div>
              <a href="get-passenger-manifest.php" class="small-box-footer">View Manifest <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="small-box bg-teal">
              <div class="inner text-center">
                <h3><?php echo $manifestCompletionRate; ?>%</h3>
                <p>Manifest Completion</p>
              </div>
              <div class="icon">
                <i class="fas fa-clipboard-check"></i>
              </div>
              <a href="get-passenger-manifest.php" class="small-box-footer">View Details <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="small-box bg-purple">
              <div class="inner text-center">
                <h3><?php echo round($totalPassengers / max($totalReservations, 1), 1); ?></h3>
                <p>Avg Passengers/Booking</p>
              </div>
              <div class="icon">
                <i class="fas fa-calculator"></i>
              </div>
              <a href="all-booking.php" class="small-box-footer">View Bookings <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-3 col-md-6 col-12 mb-4">
            <div class="small-box bg-orange">
              <div class="inner text-center">
                <h3><?php echo date('M j'); ?></h3>
                <p>Today's Date</p>
              </div>
              <div class="icon">
                <i class="fas fa-calendar-alt"></i>
              </div>
              <a href="all-booking.php?filter=today" class="small-box-footer">Today's Bookings <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
        </div>
        <!-- END: Passenger Manifest Stats -->

        <!-- QUICK ACTIONS SECTION -->
        <h4 class="dashboard-section-title">
          <i class="fas fa-bolt"></i> Quick Actions
        </h4>
        <div class="row justify-content-center mb-4">
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="all-booking.php?filter=today" class="btn btn-info btn-block btn-lg">
              <i class="fas fa-calendar-day"></i><br>
              <small>Today's Bookings</small>
            </a>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="new-bookings.php" class="btn btn-warning btn-block btn-lg">
              <i class="fas fa-plus-circle"></i><br>
              <small>New Bookings</small>
            </a>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="get-passenger-manifest.php" class="btn btn-primary btn-block btn-lg">
              <i class="fas fa-users"></i><br>
              <small>Passenger Manifest</small>
            </a>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="export-passenger-manifest.php" class="btn btn-success btn-block btn-lg">
              <i class="fas fa-download"></i><br>
              <small>Export Data</small>
            </a>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="all-booking.php" class="btn btn-secondary btn-block btn-lg">
              <i class="fas fa-list"></i><br>
              <small>All Bookings</small>
            </a>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-3">
            <a href="../website/html/booking.php" target="_blank" class="btn btn-dark btn-block btn-lg">
              <i class="fas fa-external-link-alt"></i><br>
              <small>Booking Site</small>
            </a>
          </div>
        </div>
        <!-- END: Quick Actions -->



        <!-- ANALYTICS SECTION -->
        <h4 class="dashboard-section-title">
          <i class="fas fa-chart-line"></i> Analytics & Reports
        </h4>
        <div class="row">
          <div class="col-lg-6 mb-4">
            <!-- LINE CHART CARD (Total Reservations) -->
            <div class="dashboard-line-chart card shadow" style="border-radius:30px;">
              <div class="card-header bg-info text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-line"></i> Total Reservations (Last 6 Months)</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="reservationLineChart" style="max-width:100%;min-height:220px;"></canvas>
              </div>
            </div>
          </div>
          <div class="col-lg-6 mb-4">
            <!-- BAR CHART CARD (Monthly Bookings) -->
            <div class="dashboard-bar-chart card shadow" style="border-radius:30px;">
              <div class="card-header bg-warning text-dark" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-bar"></i> Monthly Bookings</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="bookingBarChart" style="max-width:100%;min-height:220px;"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="row justify-content-center">
          <div class="col-lg-4 mb-4">
            <!-- PIE CHART CARD (Booking Status Overview) -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-info text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-pie"></i> Booking Status Overview</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="bookingPieChart" style="max-width:100%;min-height:180px;"></canvas>
              </div>
            </div>
          </div>

          <div class="col-lg-4 mb-4">
            <!-- LINE CHART CARD (Payment Method Distribution) -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-success text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-line"></i> Bookings by Payment Type</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="paymentMethodChart" style="max-width:100%;min-height:180px;"></canvas>
              </div>
            </div>
          </div>

          <div class="col-lg-4 mb-4">
            <!-- PAYMENT STATISTICS CARD -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-primary text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-bar"></i> Payment Statistics</h3>
              </div>
              <div class="card-body" style="padding:20px;">
                <div class="row">
                  <div class="col-12 mb-3">
                    <div class="info-box bg-info">
                      <span class="info-box-icon"><i class="fas fa-wallet"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">By GCash</span>
                        <span class="info-box-number" id="gcashCount">0</span>
                        <div class="progress">
                          <div class="progress-bar" id="gcashProgress"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12">
                    <div class="info-box bg-success">
                      <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">By Manual Payment</span>
                        <span class="info-box-number" id="manualCount">0</span>
                        <div class="progress">
                          <div class="progress-bar" id="manualProgress"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- END: Dashboard Charts Row -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php
  // PIE CHART DATA (Booking Status)
  $confirmed = 0;
  $cancelled = 0;
  $pending = 0;

  try {
      // Check if bookings table exists
      $check_table_sql = "SHOW TABLES LIKE 'bookings'";
      $table_result = $con->query($check_table_sql);

      if ($table_result->num_rows > 0) {
          $stmt = $con->prepare("SELECT booking_status, COUNT(*) as count FROM bookings GROUP BY booking_status");
          $stmt->execute();
          $result = $stmt->get_result();

          while($row = $result->fetch_assoc()) {
              switch($row['booking_status']) {
                  case 'confirmed':
                      $confirmed = $row['count'];
                      break;
                  case 'cancelled':
                      $cancelled = $row['count'];
                      break;
                  case 'pending':
                      $pending = $row['count'];
                      break;
              }
          }
      }
  } catch (Exception $e) {
      // Log the error but don't display it to the user
      error_log("Error in dashboard.php pie chart: " . $e->getMessage());
  }
  ?>

  <!-- Footer -->
  <?php include_once(ADMIN_ROOT . 'includes/footer.php');?>
</div>
<!-- ./wrapper -->

<div id="no-internet-overlay">No Internet Connection</div>

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="<?= ADMIN_BASE_URL ?>pages/plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap -->
<script src="<?= ADMIN_BASE_URL ?>pages/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE -->
<script src="<?= ADMIN_BASE_URL ?>pages/dist/js/adminlte.js"></script>
<!-- ChartJS -->
<script src="<?= ADMIN_BASE_URL ?>pages/plugins/chart.js/Chart.min.js"></script>
<!-- SweetAlert2 -->
<script src="<?= ADMIN_BASE_URL ?>pages/plugins/sweetalert2/sweetalert2.min.js"></script>

<!-- Note: demo.js has been disabled for production use -->

<script>
// LINE CHART (Total Reservations)
var ctx = document.getElementById('reservationLineChart').getContext('2d');
var lineChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($months); ?>,
        datasets: [{
            label: 'Total Reservations',
            data: <?php echo json_encode($monthlyCounts); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// BAR CHART (Monthly Bookings)
var ctx2 = document.getElementById('bookingBarChart').getContext('2d');
var barChart = new Chart(ctx2, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($months); ?>,
        datasets: [{
            label: 'Monthly Bookings',
            data: <?php echo json_encode($monthlyCounts); ?>,
            backgroundColor: 'rgba(255, 193, 7, 0.8)',
            borderColor: 'rgba(255, 193, 7, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// PIE CHART (Booking Status)
var ctx3 = document.getElementById('bookingPieChart').getContext('2d');
var pieChart = new Chart(ctx3, {
    type: 'pie',
    data: {
        labels: ['Confirmed', 'Cancelled', 'Pending'],
        datasets: [{
            data: [<?php echo $confirmed; ?>, <?php echo $cancelled; ?>, <?php echo $pending; ?>],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(220, 53, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(220, 53, 69, 1)',
                'rgba(255, 193, 7, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Function to handle internet connection status - ramos
function handleInternetConnection() {
    const overlay = document.getElementById('no-internet-overlay');
    if (!navigator.onLine) {
        overlay.style.display = 'flex';
        Swal.fire({
            icon: 'error',
            title: 'No Internet Connection',
            text: 'Please check your internet connection.',
            showConfirmButton: false,
            timer: 3000,
            toast: true,
            position: 'top-end'
        });

    } else {
        overlay.style.display = 'none';
    }
  }
// Event listeners for online/offline status
window.addEventListener('offline', handleInternetConnection);
window.addEventListener('online', handleInternetConnection);

// Initial check
handleInternetConnection();

// Fetch payment statistics for Payment Method Distribution chart
$.ajax({
  url: 'get-payment-stats.php',
  type: 'GET',
  dataType: 'json',
  success: function(response) {
    if(response.success) {
      // Update payment statistics info boxes
      $('#gcashCount').text(response.gcash_count);
      $('#manualCount').text(response.manual_count);

      // Calculate percentages for progress bars
      const total = response.gcash_count + response.manual_count;
      const gcashPercent = total > 0 ? (response.gcash_count / total * 100) : 0;
      const manualPercent = total > 0 ? (response.manual_count / total * 100) : 0;

      $('#gcashProgress').css('width', gcashPercent + '%');
      $('#manualProgress').css('width', manualPercent + '%');

      // Create payment method distribution line chart
      const paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
      new Chart(paymentCtx, {
        type: 'line',
        data: {
          labels: response.months,
          datasets: [
            {
              label: 'By GCash',
              data: response.gcash_monthly,
              backgroundColor: 'rgba(23, 162, 184, 0.2)',
              borderColor: 'rgba(23, 162, 184, 1)',
              borderWidth: 2,
              tension: 0.4
            },
            {
              label: 'By Manual Payment',
              data: response.manual_monthly,
              backgroundColor: 'rgba(40, 167, 69, 0.2)',
              borderColor: 'rgba(40, 167, 69, 1)',
              borderWidth: 2,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                stepSize: 1
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: 'Monthly Bookings by Payment Type'
            }
          }
        }
      });
    }
  },
  error: function() {
    console.error('Error fetching payment statistics');
  }
});

// Auto-refresh dashboard every 5 minutes
let autoRefreshEnabled = true;
let refreshInterval;

function startAutoRefresh() {
  if (autoRefreshEnabled) {
    refreshInterval = setInterval(function() {
      // Only refresh if the page is visible (user is actively viewing it)
      if (!document.hidden) {
        console.log('Auto-refreshing dashboard...');
        location.reload();
      }
    }, 300000); // 5 minutes = 300000ms
  }
}

function toggleAutoRefresh() {
  autoRefreshEnabled = !autoRefreshEnabled;
  const button = document.getElementById('autoRefreshBtn');

  if (autoRefreshEnabled) {
    startAutoRefresh();
    button.innerHTML = '<i class="fas fa-pause"></i> Pause Auto-Refresh';
    button.className = 'btn btn-warning btn-sm';
  } else {
    clearInterval(refreshInterval);
    button.innerHTML = '<i class="fas fa-play"></i> Enable Auto-Refresh';
    button.className = 'btn btn-success btn-sm';
  }
}

// Start auto-refresh when page loads
$(document).ready(function() {
  startAutoRefresh();

  // Add auto-refresh toggle button to the page
  const autoRefreshButton = `
    <div class="fixed-bottom" style="right: 20px; bottom: 20px; position: fixed; z-index: 1000;">
      <button id="autoRefreshBtn" class="btn btn-warning btn-sm" onclick="toggleAutoRefresh()">
        <i class="fas fa-pause"></i> Pause Auto-Refresh
      </button>
    </div>
  `;
  $('body').append(autoRefreshButton);
});

// Pause auto-refresh when user is not viewing the page
document.addEventListener('visibilitychange', function() {
  if (document.hidden) {
    console.log('Page hidden - pausing auto-refresh');
  } else {
    console.log('Page visible - resuming auto-refresh');
  }
});
</script>
</body>
</html>
