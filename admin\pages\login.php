<?php
/**
 * Admin Login Page
 * 
 * This is the login page for the administrative system.
 * It handles admin authentication and redirects to the dashboard upon successful login.
 */

session_start();

// Define admin system constants (only if not already defined)
if (!defined('ADMIN_SYSTEM')) {
    define('ADMIN_SYSTEM', true);
}
if (!defined('ADMIN_ROOT')) {
    define('ADMIN_ROOT', __DIR__ . '/../');
}
if (!defined('ADMIN_BASE_URL')) {
    define('ADMIN_BASE_URL', '/Online Booking Reservation System/admin/');
}

// Include admin configuration
require_once ADMIN_ROOT . 'includes/config.php';

$error = '';
$success_message = '';

// Check if admin is already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: ' . ADMIN_BASE_URL . 'pages/dashboard.php');
    exit;
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');

    if (!empty($username) && !empty($password)) {
        // Check admin credentials using mysqli
        $stmt = $con->prepare("SELECT admin_id, username, password, first_name, last_name, email, role FROM admins WHERE username = ? AND status = 'active'");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        $admin = $result->fetch_assoc();
        $stmt->close();

        if ($admin && password_verify($password, $admin['password'])) {
            // Update last login
            $stmt = $con->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
            $stmt->bind_param("i", $admin['admin_id']);
            $stmt->execute();
            $stmt->close();

            // Set session variables
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['admin_id'];
            $_SESSION['aid'] = $admin['admin_id']; // For compatibility
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
            $_SESSION['admin_email'] = $admin['email'];
            $_SESSION['admin_role'] = $admin['role'];
            $_SESSION['last_activity'] = time();

            // Set user type based on role
            if ($admin['role'] === 'subadmin') {
                $_SESSION['utype'] = 0; // Sub-admin
            } else {
                $_SESSION['utype'] = 1; // Main admin
            }

            // Set login success message
            $_SESSION['login_success'] = "You have been successfully logged in.";

            header('Location: ' . ADMIN_BASE_URL . 'pages/dashboard.php');
            exit;
        } else {
            $_SESSION['login_error'] = 'Invalid username or password. Please check your credentials and try again.';
            header('Location: ' . ADMIN_BASE_URL . 'pages/login.php');
            exit;
        }
    } else {
        $_SESSION['login_error'] = 'Please enter both username and password.';
        header('Location: ' . ADMIN_BASE_URL . 'pages/login.php');
        exit;
    }
}

// Get error from session (if any)
if (isset($_SESSION['login_error'])) {
    $error = $_SESSION['login_error'];
    unset($_SESSION['login_error']);
}

// Check for logout message
if (isset($_SESSION['logout_message'])) {
    $success_message = $_SESSION['logout_message'];
    unset($_SESSION['logout_message']);
}

// Check for timeout message
if (isset($_GET['timeout']) && $_GET['timeout'] == '1') {
    $error = 'Your session has expired. Please log in again.';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Balangay Boat Tours</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap">
    <link rel="stylesheet" href="<?= ADMIN_BASE_URL ?>assets/css/admin-login.css">
</head>
<body>
    <!-- Logo container -->
    <div class="logo-container">
        <img src="/Online Booking Reservation System/website/assets/images/carleslogomunicipality.png" alt="Municipality of Carles Logo" style="height: 80px; margin-bottom: 20px;">
    </div>

    <form class="login-container" method="POST">
        <div class="logo">
            <img src="<?= ADMIN_BASE_URL ?>assets/images/timbook-carles-tourism.png" alt="Balangay Boat Tours Logo">
        </div>
        <h2><i class="fas fa-user-shield"></i> Administrator Login</h2>
        <div style="text-align: center; margin-bottom: 1.5rem; font-size: 1.2rem; color: #4b5563;">
            <strong>Municipality of Carles</strong><br>
            Province of Iloilo
        </div>
        <?php if ($error): ?>
            <div class="error-msg"><i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?></div>
        <?php endif; ?>
        <?php if ($success_message): ?>
            <div class="success-msg"><i class="fas fa-check-circle"></i> <?= htmlspecialchars($success_message) ?></div>
        <?php endif; ?>

        <div class="input-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" required autofocus>
        </div>
        <div class="input-group">
            <label for="password">Password</label>
            <div class="password-wrapper">
                <input type="password" id="password" name="password" required>
                <span class="toggle-password" onclick="togglePassword()"><i class="fas fa-eye" id="eyeIcon"></i></span>
            </div>
        </div>
        <button class="login-btn" type="submit">Login</button>
        
        <div class="admin-notice">
            <i class="fas fa-info-circle"></i>
            This is a secure administrative area. Unauthorized access is prohibited.
        </div>
    </form>
    
    <script src="<?= ADMIN_BASE_URL ?>assets/js/admin-login.js"></script>
    
    <footer class="footer">
        &copy; 2025 Balangay Boat Tours: Sailing Through History and Nature - Carles Tourism
    </footer>
</body>
</html>
