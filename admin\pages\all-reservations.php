<?php
session_start();
error_reporting(0);
include('../includes/config.php');
if(strlen($_SESSION['aid'])==0) {
  header('location:../../../../php/pages/admin-login.php');
  exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>All Reservations</title>
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
<?php include_once("../includes/navbar.php");?>
<?php include_once("../includes/sidebar.php");?>
<div class="content-wrapper">
  <section class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6">
          <h1>All Reservations</h1>
        </div>
      </div>
    </div>
  </section>
  <section class="content">
    <div class="container-fluid">
      <div class="card">
        <div class="card-body">
          <table id="allReservations" class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>Booking Code</th>
                <th>Customer ID</th>
                <th>Boat ID</th>
                <th>Pax</th>
                <th>Start</th>
                <th>End</th>
                <th>Payment</th>
                <th>Total (₱)</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              <?php
              $sql = "SELECT * FROM bookings ORDER BY booking_id DESC";
              $query = mysqli_query($con, $sql) or die(mysqli_error($con));
              $found = false;
              while($row = mysqli_fetch_assoc($query)) {
                $found = true;
                // Status badge
                $status = strtolower($row['booking_status']);
                if ($status == 'confirmed') $badge = '<span class="badge badge-success">Confirmed</span>';
                else if ($status == 'pending') $badge = '<span class="badge badge-warning">Pending</span>';
                else if ($status == 'cancelled') $badge = '<span class="badge badge-danger">Cancelled</span>';
                else $badge = '<span class="badge badge-secondary">'.ucfirst($status).'</span>';

                echo '<tr>';
                echo '<td>'.htmlspecialchars($row['booking_code']).'</td>';
                echo '<td>'.htmlspecialchars($row['customer_id']).'</td>';
                echo '<td>'.htmlspecialchars($row['boat_id']).'</td>';
                echo '<td>'.htmlspecialchars($row['no_of_pax']).'</td>';
                echo '<td>'.htmlspecialchars($row['start_date']).'</td>';
                echo '<td>'.htmlspecialchars($row['end_date']).'</td>';
                echo '<td>'.htmlspecialchars($row['payment_method']).'</td>';
                echo '<td>'.number_format($row['total'],2).'</td>';
                echo '<td>'.$badge.'</td>';
                echo '<td>';
                echo '<a href="view-booking.php?id='.htmlspecialchars($row['booking_id']).'&ref=all-reservations" class="btn btn-info btn-sm mr-1">View</a>';
                echo '</td>';
                echo '</tr>';
              }
              if(!$found) {
                echo '<tr><td colspan="10" class="text-center text-danger">No data found in bookings table.</td></tr>';
              }
              ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </section>
</div>
<?php include_once('includes/footer.php'); ?>
</div>
<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script>
$(function () {
  $('#allReservations').DataTable({
    "responsive": true, "lengthChange": false, "autoWidth": false,
    "buttons": ["copy", "csv", "excel", "pdf", "print"]
  }).buttons().container().appendTo('#allReservations_wrapper .col-md-6:eq(0)');
});
</script>
</body>
</html>
