-- =====================================================
-- BALANGAY BOAT TOURS: SAILING THROUGH HISTORY AND NATURE
-- Complete Database Schema
-- =====================================================
-- 
-- System: Online Booking and Reservation System
-- Developed by: 4th Year BSIT Students
-- College: College of Information and Computing Studies
-- Academic Year: 2024-2025
-- 
-- This database supports:
-- - Boat booking and reservation management
-- - Passenger manifest for government compliance
-- - Payment processing (GCash & Manual)
-- - Admin dashboard analytics
-- - Tourism office reporting
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- =====================================================
-- DATABASE CREATION
-- =====================================================

CREATE DATABASE IF NOT EXISTS `balangay_boat_tours` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `balangay_boat_tours`;

-- =====================================================
-- TABLE STRUCTURE: ADMIN USERS
-- =====================================================

DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','staff') NOT NULL DEFAULT 'staff',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: BOATS
-- =====================================================

DROP TABLE IF EXISTS `boats`;
CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` varchar(50) NOT NULL,
  `capacity` int(11) NOT NULL,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `features` text DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','maintenance','retired') NOT NULL DEFAULT 'available',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`boat_id`),
  KEY `idx_availability` (`availability_status`),
  KEY `idx_capacity` (`capacity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: BOAT AVAILABILITY DATES
-- =====================================================

DROP TABLE IF EXISTS `boat_availability_dates`;
CREATE TABLE `boat_availability_dates` (
  `availability_id` int(11) NOT NULL AUTO_INCREMENT,
  `boat_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `status` enum('available','booked','maintenance','unavailable') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`availability_id`),
  UNIQUE KEY `unique_boat_date` (`boat_id`,`date`),
  KEY `idx_date` (`date`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_boat_availability_boat` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: BOOKINGS
-- =====================================================

DROP TABLE IF EXISTS `bookings`;
CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_code` varchar(20) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `contact_number` varchar(20) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` enum('Male','Female','Other') NOT NULL,
  `address` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `tour_destination` varchar(255) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` enum('gcash','manual') NOT NULL,
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `booking_status` enum('pending','confirmed','accepted','rejected','cancelled','completed') NOT NULL DEFAULT 'pending',
  `special_requests` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`booking_id`),
  UNIQUE KEY `booking_code` (`booking_code`),
  KEY `idx_boat_id` (`boat_id`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_booking_status` (`booking_status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_email` (`email`),
  CONSTRAINT `fk_booking_boat` FOREIGN KEY (`boat_id`) REFERENCES `boats` (`boat_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: PASSENGER MANIFEST
-- Government Compliance (Coast Guard, Marina, Tourism Office)
-- =====================================================

DROP TABLE IF EXISTS `passenger_manifest`;
CREATE TABLE `passenger_manifest` (
  `manifest_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `passenger_number` int(11) NOT NULL,
  `passenger_type` enum('main_booker','additional_passenger') NOT NULL DEFAULT 'additional_passenger',
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `age` int(11) NOT NULL,
  `sex` enum('Male','Female','Other') NOT NULL,
  `city` varchar(100) NOT NULL,
  `province` varchar(100) NOT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`manifest_id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_passenger_type` (`passenger_type`),
  KEY `idx_passenger_manifest_booking_passenger` (`booking_id`,`passenger_number`),
  KEY `idx_passenger_manifest_name` (`first_name`,`last_name`),
  CONSTRAINT `fk_passenger_manifest_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: PAYMENTS
-- =====================================================

DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments` (
  `payment_id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `payment_method` enum('gcash','manual','bank_transfer','cash') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_status` enum('pending','completed','failed','refunded') NOT NULL DEFAULT 'pending',
  `transaction_id` varchar(100) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`payment_id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_method` (`payment_method`),
  CONSTRAINT `fk_payment_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`booking_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: SYSTEM SETTINGS
-- =====================================================

DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- TABLE STRUCTURE: ACTIVITY LOGS
-- =====================================================

DROP TABLE IF EXISTS `activity_logs`;
CREATE TABLE `activity_logs` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('admin','customer','system') NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`log_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

DELIMITER $$

-- Check boat availability for specific date
DROP PROCEDURE IF EXISTS `CheckBoatAvailabilityForDate` $$
CREATE PROCEDURE `CheckBoatAvailabilityForDate` (IN `p_date` DATE)
BEGIN
    SELECT
        b.boat_id,
        b.name,
        b.type,
        b.capacity,
        b.price_per_day,
        COALESCE(bad.status, b.availability_status) AS availability_status,
        CASE
            WHEN bad.status IS NOT NULL THEN 'Specific date status'
            ELSE 'General status'
        END AS status_source,
        bad.notes,
        CASE
            WHEN bk.boat_id IS NOT NULL THEN 'Booked'
            ELSE 'Available'
        END AS booking_status
    FROM boats b
    LEFT JOIN boat_availability_dates bad ON b.boat_id = bad.boat_id AND bad.date = p_date
    LEFT JOIN bookings bk ON b.boat_id = bk.boat_id
        AND p_date BETWEEN bk.start_date AND bk.end_date
        AND bk.booking_status IN ('confirmed', 'accepted')
    WHERE b.availability_status = 'available'
    ORDER BY b.name;
END$$

-- Get passenger manifest for a booking
DROP PROCEDURE IF EXISTS `GetPassengerManifest` $$
CREATE PROCEDURE `GetPassengerManifest` (IN `p_booking_id` INT)
BEGIN
    SELECT
        pm.manifest_id,
        pm.booking_id,
        pm.passenger_number,
        pm.passenger_type,
        pm.first_name,
        pm.last_name,
        pm.age,
        pm.sex,
        pm.city,
        pm.province,
        pm.contact_number,
        pm.address,
        pm.created_at,
        b.booking_code,
        b.start_date,
        b.tour_destination,
        b.no_of_pax
    FROM passenger_manifest pm
    JOIN bookings b ON pm.booking_id = b.booking_id
    WHERE pm.booking_id = p_booking_id
    ORDER BY pm.passenger_number ASC;
END$$

-- Get complete booking with manifest
DROP PROCEDURE IF EXISTS `GetBookingWithManifest` $$
CREATE PROCEDURE `GetBookingWithManifest` (IN `p_booking_id` INT)
BEGIN
    -- Get booking details
    SELECT
        b.*,
        bt.name as boat_name,
        bt.capacity as boat_capacity
    FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE b.booking_id = p_booking_id;

    -- Get passenger manifest
    SELECT pm.* FROM passenger_manifest pm
    WHERE pm.booking_id = p_booking_id
    ORDER BY pm.passenger_number ASC;
END$$

-- Export passenger manifest for government reporting
DROP PROCEDURE IF EXISTS `ExportPassengerManifestForDate` $$
CREATE PROCEDURE `ExportPassengerManifestForDate` (IN `p_date` DATE)
BEGIN
    SELECT
        b.booking_code,
        b.start_date as tour_date,
        b.tour_destination,
        bt.name as boat_name,
        bt.capacity as boat_capacity,
        b.no_of_pax as total_passengers,
        pm.passenger_number,
        pm.passenger_type,
        CONCAT(pm.first_name, ' ', pm.last_name) as full_name,
        pm.age,
        pm.sex,
        pm.city,
        pm.province,
        pm.contact_number,
        pm.address,
        CASE
            WHEN pm.passenger_type = 'main_booker' THEN 'Main Contact'
            ELSE 'Passenger'
        END as role
    FROM bookings b
    JOIN passenger_manifest pm ON b.booking_id = pm.booking_id
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    WHERE DATE(b.start_date) = p_date
    AND b.booking_status IN ('confirmed', 'accepted')
    ORDER BY b.booking_code, pm.passenger_number ASC;
END$$

DELIMITER ;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default admin user
INSERT INTO `admin_users` (`username`, `password`, `email`, `full_name`, `role`, `status`) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System Administrator', 'super_admin', 'active');

-- Insert sample boats
INSERT INTO `boats` (`name`, `type`, `capacity`, `price_per_day`, `description`, `features`, `availability_status`) VALUES
('Balangay Heritage', 'Traditional Balangay', 12, 2500.00, 'Authentic traditional Filipino boat perfect for historical tours', 'Traditional design, Cultural guide, Safety equipment', 'available'),
('Island Explorer', 'Modern Tourist Boat', 20, 3500.00, 'Modern comfortable boat for island hopping adventures', 'Air conditioning, Sound system, Snorkeling gear', 'available'),
('Sunset Cruiser', 'Luxury Yacht', 15, 5000.00, 'Luxury yacht perfect for sunset tours and special occasions', 'Luxury amenities, Bar service, Professional crew', 'available'),
('Adventure Seeker', 'Speed Boat', 8, 2000.00, 'Fast boat for adventure seekers and quick island transfers', 'High speed, Adventure gear, Life jackets', 'available');

-- Insert system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`) VALUES
('system_name', 'Balangay Boat Tours: Sailing Through History and Nature', 'string', 'System name displayed throughout the application'),
('company_name', 'Balangay Boat Tours', 'string', 'Company name for official documents'),
('contact_email', '<EMAIL>', 'string', 'Main contact email'),
('contact_phone', '+63 ************', 'string', 'Main contact phone number'),
('address', 'Municipality of Carles, Province of Iloilo', 'string', 'Company address'),
('booking_advance_days', '3', 'number', 'Minimum days in advance for booking'),
('max_passengers_per_booking', '25', 'number', 'Maximum passengers allowed per booking'),
('gcash_number', '***********', 'string', 'GCash payment number'),
('gcash_name', 'Balangay Boat Tours', 'string', 'GCash account name'),
('booking_terms', 'All bookings are subject to weather conditions and availability. Cancellations must be made 24 hours in advance.', 'string', 'Booking terms and conditions'),
('passenger_manifest_required', 'true', 'boolean', 'Require passenger manifest for all bookings'),
('government_reporting_enabled', 'true', 'boolean', 'Enable government reporting features');

-- =====================================================
-- AUTO INCREMENT RESET
-- =====================================================

ALTER TABLE `admin_users` AUTO_INCREMENT = 1;
ALTER TABLE `boats` AUTO_INCREMENT = 1;
ALTER TABLE `boat_availability_dates` AUTO_INCREMENT = 1;
ALTER TABLE `bookings` AUTO_INCREMENT = 1;
ALTER TABLE `passenger_manifest` AUTO_INCREMENT = 1;
ALTER TABLE `payments` AUTO_INCREMENT = 1;
ALTER TABLE `system_settings` AUTO_INCREMENT = 1;
ALTER TABLE `activity_logs` AUTO_INCREMENT = 1;

-- =====================================================
-- FINAL SETTINGS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- =====================================================
-- INSTALLATION COMPLETE
-- =====================================================

SELECT 'Balangay Boat Tours database installation completed successfully!' as status,
       'Database: balangay_boat_tours' as database_name,
       'Tables created: 8' as tables_count,
       'Stored procedures: 4' as procedures_count,
       'Default admin: admin/password' as default_login;
